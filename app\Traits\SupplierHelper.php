<?php

namespace App\Traits;

use App\Models\Supplier;

trait SupplierHelper
{
    /**
     * Get the current supplier for the authenticated supplier admin user.
     * 
     * @return \App\Models\Supplier
     */
    protected function getCurrentSupplier()
    {
        $user = auth()->user();
        
        // Map supplier admin users to existing suppliers
        $supplierMapping = [
            'Admin Supplier 1' => 'PT. Sumber Makmur',
            'Admin Supplier 2' => 'CV. Berkah Jaya',
        ];

        $supplierName = $supplierMapping[$user->name] ?? null;
        
        if (!$supplierName) {
            // If no mapping found, create a new supplier based on user
            $supplier = Supplier::where('name', $user->name)->first();
            if (!$supplier) {
                $supplier = Supplier::create([
                    'name' => $user->name,
                    'contact_person' => $user->name,
                    'email' => $user->email,
                    'status' => 'active'
                ]);
            }
        } else {
            // Use mapped supplier
            $supplier = Supplier::where('name', $supplierName)->first();
            if (!$supplier) {
                // If mapped supplier doesn't exist, create it
                $supplier = Supplier::create([
                    'name' => $supplierName,
                    'contact_person' => $user->name,
                    'email' => $user->email,
                    'status' => 'active'
                ]);
            }
        }
        
        return $supplier;
    }
}
