<?php $__env->startSection('title', 'Detail Supplier - Indah Berkah Abadi'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Detail Supplier</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap supplier dan riwayat transaksi</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.suppliers.index')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Supplier Information -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Supplier</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nama Supplier</label>
                    <p class="text-gray-900 font-semibold"><?php echo e($supplier->name); ?></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <?php if($supplier->status === 'active'): ?> bg-green-100 text-green-800
                        <?php else: ?> bg-red-100 text-red-800
                        <?php endif; ?>">
                        <?php if($supplier->status === 'active'): ?> Aktif <?php else: ?> Tidak Aktif <?php endif; ?>
                    </span>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Kontak Person</label>
                    <p class="text-gray-900"><?php echo e($supplier->contact_person ?? '-'); ?></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nomor Telepon</label>
                    <p class="text-gray-900"><?php echo e($supplier->phone ?? '-'); ?></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <p class="text-gray-900"><?php echo e($supplier->email ?? '-'); ?></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Bergabung</label>
                    <p class="text-gray-900"><?php echo e($supplier->created_at->format('d/m/Y H:i')); ?></p>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Alamat</label>
                    <p class="text-gray-900"><?php echo e($supplier->address ?? '-'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total_deliveries'])); ?></p>
                    <p class="admin-dashboard-stat-label">Total Pengiriman</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['pending_deliveries'])); ?></p>
                    <p class="admin-dashboard-stat-label">Menunggu</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['received_deliveries'])); ?></p>
                    <p class="admin-dashboard-stat-label">Diterima</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-red-100 text-red-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total_returns'])); ?></p>
                    <p class="admin-dashboard-stat-label">Total Retur</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Deliveries -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Pengiriman Terbaru</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Kirim</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $recentDeliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <!-- <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($delivery->product->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($delivery->product->category->name ?? 'Tanpa Kategori'); ?></div>
                            </td> -->
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e(number_format($delivery->quantity)); ?></div>
                                <?php if($delivery->received_quantity && $delivery->received_quantity != $delivery->quantity): ?>
                                <div class="text-xs text-gray-500">Diterima: <?php echo e(number_format($delivery->received_quantity)); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($delivery->delivery_date->format('d/m/Y')); ?></div>
                                <?php if($delivery->received_date): ?>
                                <div class="text-xs text-gray-500">Diterima: <?php echo e($delivery->received_date->format('d/m/Y')); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php if($delivery->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($delivery->status === 'received'): ?> bg-green-100 text-green-800
                                    <?php elseif($delivery->status === 'partial'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($delivery->status === 'cancelled'): ?> bg-red-100 text-red-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php if($delivery->status === 'pending'): ?> Menunggu
                                    <?php elseif($delivery->status === 'received'): ?> Diterima
                                    <?php elseif($delivery->status === 'partial'): ?> Sebagian
                                    <?php elseif($delivery->status === 'cancelled'): ?> Dibatalkan
                                    <?php else: ?> <?php echo e(ucfirst($delivery->status)); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="<?php echo e(route('admin.supplier-deliveries.show', $delivery)); ?>" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    Lihat Detail
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Pengiriman</p>
                                    <p>Supplier ini belum memiliki riwayat pengiriman</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Recent Returns -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Retur Terbaru</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Toko</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Retur</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $recentReturns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <!-- <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->product->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($return->product->category->name ?? 'Tanpa Kategori'); ?></div>
                            </td> -->
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($return->store->name); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e(number_format($return->quantity)); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($return->return_date->format('d/m/Y')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php if($return->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($return->status === 'approved'): ?> bg-green-100 text-green-800
                                    <?php elseif($return->status === 'rejected'): ?> bg-red-100 text-red-800
                                    <?php elseif($return->status === 'completed'): ?> bg-blue-100 text-blue-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php if($return->status === 'pending'): ?> Menunggu
                                    <?php elseif($return->status === 'approved'): ?> Disetujui
                                    <?php elseif($return->status === 'rejected'): ?> Ditolak
                                    <?php elseif($return->status === 'completed'): ?> Selesai
                                    <?php else: ?> <?php echo e(ucfirst($return->status)); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="<?php echo e(route('admin.returns.show', $return)); ?>" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    Lihat Detail
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Retur</p>
                                    <p>Supplier ini belum memiliki riwayat retur</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/suppliers/show.blade.php ENDPATH**/ ?>