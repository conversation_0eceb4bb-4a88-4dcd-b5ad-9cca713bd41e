/* Indah Berkah Abadi - Supplier Dashboard JavaScript */
/* Independent JavaScript for supplier dashboard functionality */

// Supplier Dashboard Utilities
const SupplierDashboard = {
    // Initialize all dashboard functionality
    init: function() {
        this.initSidebar();
        this.initUserMenu();
        this.initDropdowns();
        this.initMobileHandlers();
        this.initFormValidation();
    },

    // Sidebar functionality
    initSidebar: function() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const toggleBtn = document.querySelector('.supplier-dashboard-mobile-menu-btn');

        if (toggleBtn) {
            // Ensure button is always clickable
            toggleBtn.style.pointerEvents = 'auto';
            toggleBtn.style.zIndex = '202';
            toggleBtn.style.position = 'relative';

            toggleBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Mobile menu button clicked'); // Debug log
                SupplierDashboard.toggleSidebar();
            });

            // Add touch event for better mobile support
            toggleBtn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Mobile menu button touched'); // Debug log
                SupplierDashboard.toggleSidebar();
            });
        }

        if (overlay) {
            overlay.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SupplierDashboard.closeSidebar();
            });

            overlay.addEventListener('touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                SupplierDashboard.closeSidebar();
            });
        }

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                SupplierDashboard.closeSidebar();
            }
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const isClickInsideSidebar = sidebar && sidebar.contains(e.target);
                const isClickOnToggleBtn = toggleBtn && toggleBtn.contains(e.target);
                const isSidebarOpen = sidebar && sidebar.classList.contains('supplier-dashboard-sidebar-open');

                if (isSidebarOpen && !isClickInsideSidebar && !isClickOnToggleBtn) {
                    SupplierDashboard.closeSidebar();
                }
            }
        });
    },

    // User menu functionality
    initUserMenu: function() {
        const userMenuButton = document.getElementById('userMenuButton');
        const userMenuDropdown = document.getElementById('userMenuDropdown');

        if (userMenuButton && userMenuDropdown) {
            // Ensure button is clickable
            userMenuButton.style.pointerEvents = 'auto';
            userMenuButton.style.zIndex = '1050';
            userMenuButton.style.position = 'relative';

            userMenuButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Sidebar user menu button clicked'); // Debug log
                userMenuDropdown.classList.toggle('supplier-dashboard-user-menu-dropdown-show');

                // Update aria-expanded
                const isExpanded = userMenuDropdown.classList.contains('supplier-dashboard-user-menu-dropdown-show');
                userMenuButton.setAttribute('aria-expanded', isExpanded);
            });

            // Add touch support
            userMenuButton.addEventListener('touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Sidebar user menu button touched'); // Debug log
                userMenuDropdown.classList.toggle('supplier-dashboard-user-menu-dropdown-show');

                const isExpanded = userMenuDropdown.classList.contains('supplier-dashboard-user-menu-dropdown-show');
                userMenuButton.setAttribute('aria-expanded', isExpanded);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userMenuButton.contains(e.target) && !userMenuDropdown.contains(e.target)) {
                    userMenuDropdown.classList.remove('supplier-dashboard-user-menu-dropdown-show');
                    userMenuButton.setAttribute('aria-expanded', 'false');
                }
            });

            userMenuDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }

        // Header user dropdown functionality
        const headerUserDropdownBtn = document.querySelector('.supplier-dashboard-user-dropdown-btn');
        const headerUserDropdown = document.getElementById('user-dropdown-menu');

        if (headerUserDropdownBtn && headerUserDropdown) {
            // Ensure button is clickable
            headerUserDropdownBtn.style.pointerEvents = 'auto';
            headerUserDropdownBtn.style.zIndex = '151';
            headerUserDropdownBtn.style.position = 'relative';

            headerUserDropdownBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Header user dropdown button clicked'); // Debug log
                headerUserDropdown.classList.toggle('supplier-dashboard-dropdown-show');
            });

            headerUserDropdownBtn.addEventListener('touchstart', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Header user dropdown button touched'); // Debug log
                headerUserDropdown.classList.toggle('supplier-dashboard-dropdown-show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!headerUserDropdownBtn.contains(e.target) && !headerUserDropdown.contains(e.target)) {
                    headerUserDropdown.classList.remove('supplier-dashboard-dropdown-show');
                }
            });
        }
    },

    // General dropdown functionality
    initDropdowns: function() {
        const dropdowns = document.querySelectorAll('.supplier-dashboard-dropdown');

        dropdowns.forEach(function(dropdown) {
            const trigger = dropdown.querySelector('.supplier-dashboard-dropdown-trigger');
            const menu = dropdown.querySelector('.supplier-dashboard-dropdown-menu');

            if (trigger && menu) {
                // Ensure trigger is clickable
                trigger.style.pointerEvents = 'auto';
                trigger.style.zIndex = '100';
                trigger.style.position = 'relative';

                trigger.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Dropdown trigger clicked'); // Debug log

                    // Close other dropdowns
                    dropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.supplier-dashboard-dropdown-menu');
                            if (otherMenu) {
                                otherMenu.classList.remove('show');
                                otherMenu.classList.remove('supplier-dashboard-dropdown-show');
                            }
                        }
                    });

                    menu.classList.toggle('show');
                    menu.classList.toggle('supplier-dashboard-dropdown-show');
                });

                // Add touch support
                trigger.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Dropdown trigger touched'); // Debug log

                    // Close other dropdowns
                    dropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown) {
                            const otherMenu = otherDropdown.querySelector('.supplier-dashboard-dropdown-menu');
                            if (otherMenu) {
                                otherMenu.classList.remove('show');
                                otherMenu.classList.remove('supplier-dashboard-dropdown-show');
                            }
                        }
                    });

                    menu.classList.toggle('show');
                    menu.classList.toggle('supplier-dashboard-dropdown-show');
                });
            }
        });

        // Close all dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            dropdowns.forEach(function(dropdown) {
                const trigger = dropdown.querySelector('.supplier-dashboard-dropdown-trigger');
                const menu = dropdown.querySelector('.supplier-dashboard-dropdown-menu');

                if (menu && !dropdown.contains(e.target)) {
                    menu.classList.remove('show');
                    menu.classList.remove('supplier-dashboard-dropdown-show');
                }
            });
        });
    },

    // Mobile-specific handlers
    initMobileHandlers: function() {
        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 1024) {
                SupplierDashboard.closeSidebar();
            }
        });

        // Prevent body scroll when sidebar is open on mobile
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isOpen = sidebar.classList.contains('supplier-dashboard-sidebar-open');
                        if (window.innerWidth < 1024) {
                            if (isOpen) {
                                document.body.style.overflow = 'hidden';
                                document.body.style.position = 'fixed';
                                document.body.style.width = '100%';
                                document.body.classList.add('supplier-sidebar-open');
                            } else {
                                document.body.style.overflow = '';
                                document.body.style.position = '';
                                document.body.style.width = '';
                                document.body.classList.remove('supplier-sidebar-open');
                            }
                        }
                    }
                });
            });

            observer.observe(sidebar, { attributes: true });
        }

        // Ensure all interactive elements are properly initialized
        this.ensureInteractiveElements();
    },

    // Ensure all interactive elements are properly set up
    ensureInteractiveElements: function() {
        // Mobile menu button
        const mobileMenuBtn = document.querySelector('.supplier-dashboard-mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.style.pointerEvents = 'auto';
            mobileMenuBtn.style.zIndex = '202';
            mobileMenuBtn.style.position = 'relative';
            mobileMenuBtn.style.cursor = 'pointer';
        }

        // All buttons
        const buttons = document.querySelectorAll('.supplier-dashboard-btn, .supplier-dashboard-btn-primary, .supplier-dashboard-btn-secondary, .supplier-dashboard-btn-sm, .supplier-dashboard-btn-danger');
        buttons.forEach(function(btn) {
            btn.style.pointerEvents = 'auto';
            btn.style.position = 'relative';
            btn.style.cursor = 'pointer';
        });

        // Form elements
        const formElements = document.querySelectorAll('.supplier-dashboard-form-input, .supplier-dashboard-form-select, .supplier-dashboard-form-textarea');
        formElements.forEach(function(element) {
            element.style.pointerEvents = 'auto';
            element.style.position = 'relative';
        });

        // Navigation items
        const navItems = document.querySelectorAll('.supplier-dashboard-nav-item');
        navItems.forEach(function(item) {
            item.style.pointerEvents = 'auto';
            item.style.position = 'relative';
            item.style.cursor = 'pointer';
        });
    },

    // Form validation helpers
    initFormValidation: function() {
        const forms = document.querySelectorAll('.supplier-dashboard-form');
        
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;
                
                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('supplier-dashboard-input-error');
                    } else {
                        field.classList.remove('supplier-dashboard-input-error');
                    }
                });
                
                if (!isValid) {
                    e.preventDefault();
                    SupplierDashboard.showAlert('Mohon lengkapi semua field yang wajib diisi.', 'error');
                }
            });
        });
    },

    // Sidebar toggle functions
    toggleSidebar: function() {
        console.log('toggleSidebar called'); // Debug log
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar && overlay) {
            const isOpen = sidebar.classList.contains('supplier-dashboard-sidebar-open');
            console.log('Sidebar is currently:', isOpen ? 'open' : 'closed'); // Debug log

            if (isOpen) {
                this.closeSidebar();
            } else {
                this.openSidebar();
            }
        } else {
            console.error('Sidebar or overlay not found'); // Debug log
        }
    },

    openSidebar: function() {
        console.log('openSidebar called'); // Debug log
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar && overlay) {
            sidebar.classList.add('supplier-dashboard-sidebar-open');
            overlay.classList.add('supplier-dashboard-sidebar-overlay-show');
            document.body.classList.add('supplier-sidebar-open');

            // Prevent background scrolling on mobile
            if (window.innerWidth <= 768) {
                document.body.style.overflow = 'hidden';
                document.body.style.position = 'fixed';
                document.body.style.width = '100%';
            }

            // Focus management for accessibility
            const firstNavItem = sidebar.querySelector('.supplier-dashboard-nav-item');
            if (firstNavItem) {
                setTimeout(() => firstNavItem.focus(), 100);
            }
        }
    },

    closeSidebar: function() {
        console.log('closeSidebar called'); // Debug log
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        if (sidebar && overlay) {
            sidebar.classList.remove('supplier-dashboard-sidebar-open');
            overlay.classList.remove('supplier-dashboard-sidebar-overlay-show');
            document.body.classList.remove('supplier-sidebar-open');

            // Restore scrolling
            document.body.style.overflow = '';
            document.body.style.position = '';
            document.body.style.width = '';

            // Return focus to toggle button
            const toggleBtn = document.querySelector('.supplier-dashboard-mobile-menu-btn');
            if (toggleBtn && window.innerWidth <= 768) {
                setTimeout(() => toggleBtn.focus(), 100);
            }
        }
    },

    // Alert system
    showAlert: function(message, type = 'info') {
        const alertContainer = document.querySelector('.supplier-dashboard-content');
        if (!alertContainer) return;

        const alertElement = document.createElement('div');
        alertElement.className = `supplier-dashboard-alert supplier-dashboard-alert-${type}`;
        alertElement.innerHTML = `
            <svg class="supplier-dashboard-alert-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>${message}</span>
        `;

        alertContainer.insertBefore(alertElement, alertContainer.firstChild);

        // Auto-remove after 5 seconds
        setTimeout(function() {
            if (alertElement.parentNode) {
                alertElement.remove();
            }
        }, 5000);
    },

    // Loading state helpers
    showLoading: function(element) {
        if (element) {
            element.classList.add('supplier-dashboard-loading');
            element.disabled = true;
        }
    },

    hideLoading: function(element) {
        if (element) {
            element.classList.remove('supplier-dashboard-loading');
            element.disabled = false;
        }
    },

    // Utility functions
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('id-ID', {
            style: 'currency',
            currency: 'IDR',
            minimumFractionDigits: 0
        }).format(amount);
    },

    formatDate: function(date) {
        return new Intl.DateTimeFormat('id-ID', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(new Date(date));
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    SupplierDashboard.init();
});

// Make functions globally available for inline handlers
window.toggleSidebar = function() {
    console.log('Global toggleSidebar called'); // Debug log
    SupplierDashboard.toggleSidebar();
};

window.closeSidebar = function() {
    console.log('Global closeSidebar called'); // Debug log
    SupplierDashboard.closeSidebar();
};

window.toggleUserMenu = function() {
    console.log('Global toggleUserMenu called'); // Debug log
    const dropdown = document.getElementById('user-dropdown-menu');
    if (dropdown) {
        dropdown.classList.toggle('supplier-dashboard-dropdown-show');
        console.log('Header dropdown toggled'); // Debug log
    } else {
        console.error('Header dropdown not found'); // Debug log
    }
};

// Additional debugging function
window.debugSupplierDashboard = function() {
    console.log('=== Supplier Dashboard Debug Info ===');
    console.log('Sidebar:', document.getElementById('sidebar'));
    console.log('Overlay:', document.getElementById('sidebar-overlay'));
    console.log('Mobile Menu Button:', document.querySelector('.supplier-dashboard-mobile-menu-btn'));
    console.log('Header Dropdown:', document.getElementById('user-dropdown-menu'));
    console.log('Sidebar User Menu:', document.getElementById('userMenuDropdown'));
    console.log('Window width:', window.innerWidth);
    console.log('=====================================');
};

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupplierDashboard;
}
