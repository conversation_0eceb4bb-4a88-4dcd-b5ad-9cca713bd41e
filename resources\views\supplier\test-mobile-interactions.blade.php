@extends('layouts.supplier')

@section('page-title', 'Test Mobile Interactions')

@section('content')
<div class="supplier-dashboard-content">
    <!-- Test Header -->
    <div class="supplier-dashboard-header-section">
        <div class="supplier-dashboard-page-header">
            <h1 class="supplier-dashboard-page-title">Test Mobile Interactions</h1>
            <p class="supplier-dashboard-page-description">
                Halaman ini untuk menguji interaksi mobile pada dashboard supplier
            </p>
        </div>
    </div>

    <!-- Test Cards -->
    <div class="supplier-dashboard-grid supplier-dashboard-grid-cols-1 supplier-dashboard-grid-md-2 supplier-dashboard-gap-6">
        <!-- Mobile Menu Test Card -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Test Mobile Menu</h3>
            </div>
            <div class="supplier-dashboard-card-body">
                <p class="supplier-dashboard-text-sm supplier-dashboard-text-gray-600 supplier-dashboard-mb-4">
                    Klik tombol hamburger di header untuk membuka/menutup sidebar
                </p>
                <div class="supplier-dashboard-flex supplier-dashboard-gap-2">
                    <button type="button" class="supplier-dashboard-btn supplier-dashboard-btn-primary" onclick="toggleSidebar()">
                        Toggle Sidebar
                    </button>
                    <button type="button" class="supplier-dashboard-btn supplier-dashboard-btn-secondary" onclick="closeSidebar()">
                        Close Sidebar
                    </button>
                </div>
            </div>
        </div>

        <!-- Dropdown Test Card -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Test Dropdowns</h3>
            </div>
            <div class="supplier-dashboard-card-body">
                <p class="supplier-dashboard-text-sm supplier-dashboard-text-gray-600 supplier-dashboard-mb-4">
                    Test dropdown di header dan sidebar
                </p>
                <div class="supplier-dashboard-flex supplier-dashboard-gap-2">
                    <button type="button" class="supplier-dashboard-btn supplier-dashboard-btn-primary" onclick="toggleUserMenu()">
                        Toggle Header Dropdown
                    </button>
                    <button type="button" class="supplier-dashboard-btn supplier-dashboard-btn-secondary" onclick="debugSupplierDashboard()">
                        Debug Info
                    </button>
                </div>
            </div>
        </div>

        <!-- Form Elements Test Card -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Test Form Elements</h3>
            </div>
            <div class="supplier-dashboard-card-body">
                <div class="supplier-dashboard-form-group supplier-dashboard-mb-4">
                    <label class="supplier-dashboard-form-label">Test Input</label>
                    <input type="text" class="supplier-dashboard-form-input" placeholder="Test input field">
                </div>
                <div class="supplier-dashboard-form-group supplier-dashboard-mb-4">
                    <label class="supplier-dashboard-form-label">Test Select</label>
                    <select class="supplier-dashboard-form-select">
                        <option>Option 1</option>
                        <option>Option 2</option>
                        <option>Option 3</option>
                    </select>
                </div>
                <div class="supplier-dashboard-form-group">
                    <button type="button" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        Test Submit
                    </button>
                </div>
            </div>
        </div>

        <!-- Z-Index Test Card -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Z-Index Test</h3>
            </div>
            <div class="supplier-dashboard-card-body">
                <p class="supplier-dashboard-text-sm supplier-dashboard-text-gray-600 supplier-dashboard-mb-4">
                    Test z-index hierarchy pada mobile
                </p>
                <div class="supplier-dashboard-dropdown-container-enhanced supplier-dashboard-mb-4">
                    <div class="supplier-dashboard-dropdown">
                        <button type="button" class="supplier-dashboard-dropdown-trigger supplier-dashboard-btn supplier-dashboard-btn-secondary">
                            Test Dropdown
                            <svg class="supplier-dashboard-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="supplier-dashboard-dropdown-menu">
                            <a href="#" class="supplier-dashboard-dropdown-item">Item 1</a>
                            <a href="#" class="supplier-dashboard-dropdown-item">Item 2</a>
                            <a href="#" class="supplier-dashboard-dropdown-item">Item 3</a>
                        </div>
                    </div>
                </div>
                <div class="supplier-dashboard-form-group-enhanced">
                    <select class="supplier-dashboard-form-select-enhanced">
                        <option>Enhanced Select 1</option>
                        <option>Enhanced Select 2</option>
                        <option>Enhanced Select 3</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Instructions -->
    <div class="supplier-dashboard-card supplier-dashboard-mt-6">
        <div class="supplier-dashboard-card-header">
            <h3 class="supplier-dashboard-card-title">Instruksi Test Mobile</h3>
        </div>
        <div class="supplier-dashboard-card-body">
            <div class="supplier-dashboard-text-sm supplier-dashboard-text-gray-600">
                <h4 class="supplier-dashboard-font-semibold supplier-dashboard-mb-2">Langkah-langkah test:</h4>
                <ol class="supplier-dashboard-list-decimal supplier-dashboard-list-inside supplier-dashboard-space-y-1">
                    <li>Buka halaman ini di mobile device atau resize browser ke ukuran mobile (&lt; 768px)</li>
                    <li>Test hamburger menu button - harus bisa diklik dan membuka sidebar</li>
                    <li>Test dropdown di header (klik avatar/nama user) - harus bisa diklik dan menampilkan menu</li>
                    <li>Test dropdown di sidebar footer - harus bisa diklik</li>
                    <li>Test semua button dan form element - harus responsive dan clickable</li>
                    <li>Test overlay - klik di luar sidebar harus menutup sidebar</li>
                    <li>Test escape key - harus menutup sidebar</li>
                </ol>
                <h4 class="supplier-dashboard-font-semibold supplier-dashboard-mt-4 supplier-dashboard-mb-2">Debug:</h4>
                <p>Buka console browser dan panggil <code>debugSupplierDashboard()</code> untuk melihat informasi debug.</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
/* Additional test styles */
.supplier-dashboard-dropdown-arrow {
    width: 1rem;
    height: 1rem;
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.supplier-dashboard-dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    z-index: 700;
    margin-top: 0.25rem;
}

.supplier-dashboard-dropdown-menu.show,
.supplier-dashboard-dropdown-menu.supplier-dashboard-dropdown-show {
    display: block;
}

.supplier-dashboard-dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: #374151;
    text-decoration: none;
    border-bottom: 1px solid #f3f4f6;
}

.supplier-dashboard-dropdown-item:hover {
    background-color: #f9fafb;
    color: #111827;
}

.supplier-dashboard-dropdown-item:last-child {
    border-bottom: none;
}

.supplier-dashboard-dropdown {
    position: relative;
}

code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: monospace;
    font-size: 0.875rem;
}
</style>
@endpush
