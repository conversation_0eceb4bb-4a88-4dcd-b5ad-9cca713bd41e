<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;

class SupplierDeliverySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $suppliers = Supplier::active()->get();
        $products = Product::all();
        $admin = User::where('role', 'admin')->first();

        if ($suppliers->count() > 0 && $products->count() > 0 && $admin) {
            // Create sample supplier deliveries with different statuses
            $deliveries = [
                [
                    'supplier' => $suppliers->first(),
                    'product' => $products->first(),
                    'quantity' => 100,
                    'received_quantity' => 100,
                    'unit_price' => 50000,
                    'delivery_date' => Carbon::now()->subDays(5),
                    'received_date' => Carbon::now()->subDays(3),
                    'status' => 'received',
                    'notes' => 'Pengiriman diterima dengan baik',
                    'received_by' => $admin->id,
                ],
                [
                    'supplier' => $suppliers->skip(1)->first() ?? $suppliers->first(),
                    'product' => $products->skip(1)->first() ?? $products->first(),
                    'quantity' => 75,
                    'received_quantity' => 70,
                    'unit_price' => 75000,
                    'delivery_date' => Carbon::now()->subDays(3),
                    'received_date' => Carbon::now()->subDays(1),
                    'status' => 'partial',
                    'notes' => 'Diterima sebagian, ada kerusakan pada 5 unit',
                    'received_by' => $admin->id,
                ],
                [
                    'supplier' => $suppliers->skip(2)->first() ?? $suppliers->first(),
                    'product' => $products->skip(2)->first() ?? $products->first(),
                    'quantity' => 50,
                    'received_quantity' => null,
                    'unit_price' => 25000,
                    'delivery_date' => Carbon::now()->addDays(2),
                    'received_date' => null,
                    'status' => 'pending',
                    'notes' => 'Menunggu pengiriman dari supplier',
                    'received_by' => null,
                ],
                [
                    'supplier' => $suppliers->first(),
                    'product' => $products->skip(3)->first() ?? $products->first(),
                    'quantity' => 200,
                    'received_quantity' => 180,
                    'unit_price' => 15000,
                    'delivery_date' => Carbon::now()->subDays(7),
                    'received_date' => Carbon::now()->subDays(5),
                    'status' => 'partial',
                    'notes' => 'Sebagian produk rusak dalam perjalanan',
                    'received_by' => $admin->id,
                ],
                [
                    'supplier' => $suppliers->skip(1)->first() ?? $suppliers->first(),
                    'product' => $products->skip(4)->first() ?? $products->first(),
                    'quantity' => 30,
                    'received_quantity' => null,
                    'unit_price' => 120000,
                    'delivery_date' => Carbon::now()->subDays(2),
                    'received_date' => null,
                    'status' => 'cancelled',
                    'notes' => 'Dibatalkan karena produk tidak sesuai spesifikasi',
                    'received_by' => null,
                ],
            ];

            foreach ($deliveries as $deliveryData) {
                $totalPrice = $deliveryData['quantity'] * $deliveryData['unit_price'];
                
                SupplierDelivery::create([
                    'supplier_id' => $deliveryData['supplier']->id,
                    'product_id' => $deliveryData['product']->id,
                    'quantity' => $deliveryData['quantity'],
                    'received_quantity' => $deliveryData['received_quantity'],
                    'unit_price' => $deliveryData['unit_price'],
                    'total_price' => $totalPrice,
                    'delivery_date' => $deliveryData['delivery_date'],
                    'received_date' => $deliveryData['received_date'],
                    'status' => $deliveryData['status'],
                    'notes' => $deliveryData['notes'],
                    'received_by' => $deliveryData['received_by'],
                ]);
            }

            $this->command->info('Created ' . count($deliveries) . ' sample supplier deliveries');
        } else {
            $this->command->warn('Cannot create supplier deliveries: missing suppliers, products, or admin user');
        }
    }
}
