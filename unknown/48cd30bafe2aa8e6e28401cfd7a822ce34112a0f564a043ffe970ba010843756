@extends('layouts.supplier')

@section('title', 'Tambah Pengiriman - Dashboard Supplier')
@section('page-title', 'Tambah Pengiriman')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Tambah Pengiriman Baru</h1>
                    <p class="text-gray-600 mt-1">Buat pengiriman produk ke gudang</p>
                </div>
                <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Ke<PERSON>li
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Informasi Pengiriman</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <form method="POST" action="{{ route('supplier.deliveries.store') }}" class="space-y-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Warehouse Admin -->
                    <div>
                        <label for="warehouse_admin_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Kirim ke Admin Gudang <span class="text-red-500">*</span>
                        </label>
                        <select id="warehouse_admin_id"
                                name="warehouse_admin_id"
                                class="supplier-dashboard-select @error('warehouse_admin_id') supplier-dashboard-input-error @enderror"
                                required>
                            <option value="">Pilih Admin Gudang</option>
                            @foreach($warehouseAdmins as $admin)
                                <option value="{{ $admin->id }}" {{ old('warehouse_admin_id') == $admin->id ? 'selected' : '' }}>
                                    {{ $admin->name }} ({{ $admin->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('warehouse_admin_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Product -->
                    <div>
                        <label for="product_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Produk <span class="text-red-500">*</span>
                        </label>
                        <div class="supplier-dashboard-product-selector">
                            <select id="product_id"
                                    name="product_id"
                                    class="supplier-dashboard-select @error('product_id') supplier-dashboard-input-error @enderror"
                                    required>
                                @if($products->count() > 0)
                                    <option value="">Pilih Produk</option>
                                    @foreach($products as $product)
                                        <option value="{{ $product->id }}" {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                            {{ $product->name }}
                                        </option>
                                    @endforeach
                                    <option value="add_new" class="supplier-dashboard-add-new-option">+ Tambah Produk Baru</option>
                                @else
                                    <option value="">Belum ada produk - Silakan tambah produk baru</option>
                                    <option value="add_new" class="supplier-dashboard-add-new-option" selected>+ Tambah Produk Baru</option>
                                @endif
                            </select>

                            <!-- Inline Product Creation Form -->
                            <div id="new_product_form" class="supplier-dashboard-new-product-form" style="display: none;">
                                <div class="flex gap-2 mt-2">
                                    <input type="text"
                                           id="new_product_name"
                                           placeholder="Masukkan nama produk baru"
                                           class="supplier-dashboard-input flex-1">
                                    <button type="button"
                                            id="save_new_product"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary px-4">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </button>
                                    <button type="button"
                                            id="cancel_new_product"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-secondary px-4">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">Produk baru akan langsung tersedia untuk dipilih</p>
                            </div>
                        </div>
                        @error('product_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror

                        @if($products->count() == 0)
                            <div class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <p class="text-sm font-medium text-blue-800">Belum Ada Produk</p>
                                        <p class="text-sm text-blue-700 mt-1">Sistem belum memiliki produk yang tersedia. Silakan tambah produk baru terlebih dahulu untuk melanjutkan pembuatan pengiriman.</p>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah <span class="text-red-500">*</span>
                        </label>
                        <input type="number"
                               id="quantity"
                               name="quantity"
                               value="{{ old('quantity') }}"
                               min="1"
                               class="supplier-dashboard-input @error('quantity') supplier-dashboard-input-error @enderror"
                               placeholder="Masukkan jumlah"
                               required>
                        @error('quantity')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Unit Price -->
                    <div>
                        <label for="unit_price" class="block text-sm font-medium text-gray-700 mb-2">
                            Harga Satuan (Opsional)
                        </label>
                        <input type="number"
                               id="unit_price"
                               name="unit_price"
                               value="{{ old('unit_price') }}"
                               min="0"
                               step="0.01"
                               class="supplier-dashboard-input @error('unit_price') supplier-dashboard-input-error @enderror"
                               placeholder="Masukkan harga satuan">
                        @error('unit_price')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Delivery Date -->
                    <div>
                        <label for="delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="delivery_date"
                               name="delivery_date"
                               value="{{ old('delivery_date', date('Y-m-d')) }}"
                               class="supplier-dashboard-input @error('delivery_date') supplier-dashboard-input-error @enderror"
                               required>
                        @error('delivery_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Total Price Display -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Total Harga (Otomatis)
                        </label>
                        <input type="text"
                               id="total_price_display"
                               class="supplier-dashboard-input bg-gray-50"
                               placeholder="Akan dihitung otomatis"
                               readonly>
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                        Catatan (Opsional)
                    </label>
                    <textarea id="notes"
                              name="notes"
                              rows="4"
                              class="supplier-dashboard-textarea @error('notes') supplier-dashboard-input-error @enderror"
                              placeholder="Tambahkan catatan untuk pengiriman ini...">{{ old('notes') }}</textarea>
                    @error('notes')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
                    <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Pengiriman
                    </button>
                    <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const unitPriceInput = document.getElementById('unit_price');
    const totalPriceDisplay = document.getElementById('total_price_display');
    const productSelect = document.getElementById('product_id');
    const newProductForm = document.getElementById('new_product_form');
    const newProductNameInput = document.getElementById('new_product_name');
    const saveNewProductBtn = document.getElementById('save_new_product');
    const cancelNewProductBtn = document.getElementById('cancel_new_product');

    // Calculate total price
    function calculateTotal() {
        const quantity = parseFloat(quantityInput.value) || 0;
        const unitPrice = parseFloat(unitPriceInput.value) || 0;
        const total = quantity * unitPrice;

        if (total > 0) {
            totalPriceDisplay.value = 'Rp ' + total.toLocaleString('id-ID', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        } else {
            totalPriceDisplay.value = '';
        }
    }

    // Handle product selection change
    function handleProductSelection() {
        if (productSelect.value === 'add_new') {
            newProductForm.style.display = 'block';
            productSelect.style.display = 'none';
            newProductNameInput.focus();
        } else {
            newProductForm.style.display = 'none';
            productSelect.style.display = 'block';
        }
    }

    // Handle new product creation
    function createNewProduct() {
        const productName = newProductNameInput.value.trim();

        if (!productName) {
            alert('Nama produk tidak boleh kosong');
            newProductNameInput.focus();
            return;
        }

        // Disable button during request
        saveNewProductBtn.disabled = true;
        saveNewProductBtn.innerHTML = '<svg class="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>';

        // Send AJAX request to create product
        fetch('{{ route("supplier.products.create") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                name: productName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Add new product to dropdown
                const newOption = document.createElement('option');
                newOption.value = data.product.id;
                newOption.textContent = data.product.name;
                newOption.selected = true;

                // Insert before "Add New Product" option
                const addNewOption = productSelect.querySelector('option[value="add_new"]');
                productSelect.insertBefore(newOption, addNewOption);

                // Update the first option if it was the "no products" message
                const firstOption = productSelect.querySelector('option[value=""]');
                if (firstOption && firstOption.textContent.includes('Belum ada produk')) {
                    firstOption.textContent = 'Pilih Produk';
                }

                // Remove "selected" attribute from "add_new" option
                addNewOption.removeAttribute('selected');

                // Reset form and show success
                newProductNameInput.value = '';
                newProductForm.style.display = 'none';
                productSelect.style.display = 'block';

                // Show success message
                showNotification(data.message, 'success');
            } else {
                alert('Gagal membuat produk: ' + (data.message || 'Terjadi kesalahan'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat membuat produk');
        })
        .finally(() => {
            // Re-enable button
            saveNewProductBtn.disabled = false;
            saveNewProductBtn.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
        });
    }

    // Handle cancel new product
    function cancelNewProduct() {
        newProductNameInput.value = '';
        newProductForm.style.display = 'none';
        productSelect.style.display = 'block';
        productSelect.value = '';
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${type === 'success' ? 'bg-green-500' : 'bg-blue-500'} text-white`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Event listeners
    quantityInput.addEventListener('input', calculateTotal);
    unitPriceInput.addEventListener('input', calculateTotal);
    productSelect.addEventListener('change', handleProductSelection);
    saveNewProductBtn.addEventListener('click', createNewProduct);
    cancelNewProductBtn.addEventListener('click', cancelNewProduct);

    // Handle Enter key in new product name input
    newProductNameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            createNewProduct();
        }
    });

    // Calculate on page load if values exist
    calculateTotal();

    // Auto-show product creation form if no products available or "add_new" is selected
    if (productSelect.value === 'add_new') {
        handleProductSelection();
    }
});
</script>
@endpush
@endsection
