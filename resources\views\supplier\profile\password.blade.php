@extends('layouts.supplier')

@section('title', 'Ubah Password - Indah Berkah Abadi')
@section('page-title', 'Ubah Password')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Ubah Password</h1>
                    <p class="text-gray-600 mt-1">Perbarui kata sandi untuk keamanan akun</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="max-w-md">
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h2 class="supplier-dashboard-card-title">Ubah Password</h2>
                <p class="text-sm text-gray-600">Pastikan menggunakan kata sandi yang kuat dan aman</p>
            </div>
            <div class="supplier-dashboard-card-content">
                <form method="POST" action="{{ route('supplier.profile.password.update') }}" class="space-y-6">
                    @csrf
                    @method('PUT')
                    
                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Kata Sandi Baru <span class="text-red-500">*</span>
                        </label>
                        <input 
                            id="password" 
                            name="password" 
                            type="password" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror"
                            placeholder="Masukkan kata sandi baru"
                            required
                        >
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        <p class="mt-1 text-xs text-gray-500">
                            Minimal 8 karakter dengan kombinasi huruf, angka, dan simbol
                        </p>
                    </div>

                    <!-- Password Confirmation Field -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Konfirmasi Kata Sandi <span class="text-red-500">*</span>
                        </label>
                        <input 
                            id="password_confirmation" 
                            name="password_confirmation" 
                            type="password" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="Ulangi kata sandi baru"
                            required
                        >
                        <p class="mt-1 text-xs text-gray-500">
                            Masukkan ulang kata sandi untuk konfirmasi
                        </p>
                    </div>

                    <!-- Security Notice -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex">
                            <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-800">Tips Keamanan</h4>
                                <div class="mt-1 text-sm text-yellow-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Gunakan kombinasi huruf besar, kecil, angka, dan simbol</li>
                                        <li>Hindari menggunakan informasi pribadi yang mudah ditebak</li>
                                        <li>Jangan gunakan kata sandi yang sama dengan akun lain</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('supplier.profile.show') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                            Batal
                        </a>
                        <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Ubah Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
