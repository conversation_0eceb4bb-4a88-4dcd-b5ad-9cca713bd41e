<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\SupplierDelivery;
use App\Models\Product;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;

class CancelledDeliverySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get required data
        $products = Product::all();
        $suppliers = Supplier::where('status', 'active')->get();
        $admin = User::where('role', 'admin')->first();

        if ($products->count() > 0 && $suppliers->count() > 0 && $admin) {
            // Create sample cancelled deliveries
            $cancelledDeliveries = [
                [
                    'supplier_id' => $suppliers->first()->id,
                    'product_id' => $products->first()->id,
                    'quantity' => 50,
                    'received_quantity' => null,
                    'unit_price' => 25000,
                    'total_price' => 1250000,
                    'delivery_date' => Carbon::now()->subDays(3),
                    'received_date' => null,
                    'status' => 'cancelled',
                    'notes' => 'Pengiriman dibatalkan karena produk tidak tersedia',
                    'received_by' => null,
                ],
                [
                    'supplier_id' => $suppliers->skip(1)->first()->id ?? $suppliers->first()->id,
                    'product_id' => $products->skip(1)->first()->id,
                    'quantity' => 30,
                    'received_quantity' => null,
                    'unit_price' => 15000,
                    'total_price' => 450000,
                    'delivery_date' => Carbon::now()->subDays(5),
                    'received_date' => null,
                    'status' => 'cancelled',
                    'notes' => 'Dibatalkan karena masalah kualitas produk',
                    'received_by' => null,
                ],
                [
                    'supplier_id' => $suppliers->first()->id,
                    'product_id' => $products->skip(2)->first()->id,
                    'quantity' => 75,
                    'received_quantity' => null,
                    'unit_price' => 35000,
                    'total_price' => 2625000,
                    'delivery_date' => Carbon::now()->subDays(7),
                    'received_date' => null,
                    'status' => 'cancelled',
                    'notes' => 'Pengiriman terlambat, dibatalkan oleh admin',
                    'received_by' => null,
                ],
            ];

            foreach ($cancelledDeliveries as $deliveryData) {
                SupplierDelivery::firstOrCreate(
                    [
                        'supplier_id' => $deliveryData['supplier_id'],
                        'product_id' => $deliveryData['product_id'],
                        'delivery_date' => $deliveryData['delivery_date'],
                        'status' => 'cancelled',
                    ],
                    $deliveryData
                );
            }

            $this->command->info('Created sample cancelled delivery data');
        } else {
            $this->command->warn('Insufficient data to create cancelled deliveries. Make sure products, suppliers, and admin users exist.');
        }
    }
}
