<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Supplier;
use App\Models\SupplierDelivery;
use App\Models\ReturnModel;
use Illuminate\Validation\Rule;

class AdminSupplierController extends Controller
{
    /**
     * Display a listing of suppliers.
     */
    public function index(Request $request)
    {
        $query = Supplier::query();

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('contact_person', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        if (in_array($sortBy, ['name', 'contact_person', 'status', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        $suppliers = $query->paginate(15);

        // Get statistics
        $stats = [
            'total' => Supplier::count(),
            'active' => Supplier::where('status', 'active')->count(),
            'inactive' => Supplier::where('status', 'inactive')->count(),
            'total_deliveries' => SupplierDelivery::count(),
            'pending_deliveries' => SupplierDelivery::where('status', 'pending')->count(),
        ];

        return view('admin.suppliers.index', compact('suppliers', 'stats'));
    }

    /**
     * Show the form for creating a new supplier.
     */
    public function create()
    {
        return view('admin.suppliers.create');
    }

    /**
     * Store a newly created supplier in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:suppliers,name',
            'contact_person' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255|unique:suppliers,email',
            'address' => 'nullable|string|max:1000',
            'status' => 'required|in:active,inactive',
        ], [
            'name.required' => 'Nama supplier wajib diisi',
            'name.string' => 'Nama supplier harus berupa teks',
            'name.max' => 'Nama supplier maksimal 255 karakter',
            'name.unique' => 'Nama supplier sudah digunakan',
            'contact_person.string' => 'Nama kontak harus berupa teks',
            'contact_person.max' => 'Nama kontak maksimal 255 karakter',
            'phone.string' => 'Nomor telepon harus berupa teks',
            'phone.max' => 'Nomor telepon maksimal 20 karakter',
            'email.email' => 'Format email tidak valid',
            'email.max' => 'Email maksimal 255 karakter',
            'email.unique' => 'Email sudah digunakan',
            'address.string' => 'Alamat harus berupa teks',
            'address.max' => 'Alamat maksimal 1000 karakter',
            'status.required' => 'Status wajib dipilih',
            'status.in' => 'Status tidak valid',
        ]);

        $supplier = Supplier::create($validatedData);

        return redirect()->route('admin.suppliers.index')
            ->with('success', 'Supplier berhasil ditambahkan: ' . $supplier->name);
    }

    /**
     * Display the specified supplier.
     */
    public function show(Supplier $supplier)
    {
        $supplier->load(['supplierDeliveries.product', 'returns.product']);
        
        // Get supplier statistics
        $stats = [
            'total_deliveries' => $supplier->supplierDeliveries()->count(),
            'pending_deliveries' => $supplier->supplierDeliveries()->where('status', 'pending')->count(),
            'received_deliveries' => $supplier->supplierDeliveries()->where('status', 'received')->count(),
            'total_returns' => $supplier->returns()->count(),
            'pending_returns' => $supplier->returns()->where('status', 'requested')->count(),
            'completed_returns' => $supplier->returns()->where('status', 'completed')->count(),
        ];

        // Get recent deliveries
        $recentDeliveries = $supplier->supplierDeliveries()
            ->with(['product'])
            ->orderBy('delivery_date', 'desc')
            ->limit(10)
            ->get();

        // Get recent returns
        $recentReturns = $supplier->returns()
            ->with(['product', 'store'])
            ->orderBy('return_date', 'desc')
            ->limit(10)
            ->get();

        return view('admin.suppliers.show', compact('supplier', 'stats', 'recentDeliveries', 'recentReturns'));
    }


    /**
     * Remove the specified supplier from storage.
     */
    public function destroy(Supplier $supplier)
    {
        // Check if supplier has deliveries or returns
        $deliveriesCount = $supplier->supplierDeliveries()->count();
        $returnsCount = $supplier->returns()->count();

        if ($deliveriesCount > 0 || $returnsCount > 0) {
            return redirect()->route('admin.suppliers.index')
                ->with('error', 'Supplier tidak dapat dihapus karena memiliki riwayat pengiriman atau retur');
        }

        $supplierName = $supplier->name;
        $supplier->delete();

        return redirect()->route('admin.suppliers.index')
            ->with('success', 'Supplier berhasil dihapus: ' . $supplierName);
    }
}
