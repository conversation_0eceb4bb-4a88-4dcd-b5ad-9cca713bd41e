<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\DashboardStatsService;
use App\Services\RecentActivityService;
use App\Services\DistributionAnalyticsService;

class AdminDashboardController extends Controller
{
    protected $dashboardStatsService;
    protected $recentActivityService;
    protected $distributionAnalyticsService;

    public function __construct(
        DashboardStatsService $dashboardStatsService,
        RecentActivityService $recentActivityService,
        DistributionAnalyticsService $distributionAnalyticsService
    ) {
        $this->dashboardStatsService = $dashboardStatsService;
        $this->recentActivityService = $recentActivityService;
        $this->distributionAnalyticsService = $distributionAnalyticsService;
    }

    public function index(Request $request)
    {
        // Get real statistics from database
        $stats = $this->dashboardStatsService->getStats();
        $detailedStats = $this->dashboardStatsService->getDetailedStats();
        $monthlyStats = $this->dashboardStatsService->getMonthlyStats();

        // Get real recent activities
        $recentActivities = $this->recentActivityService->getRecentActivities(8);

        // Get distribution analytics data
        $analytics = $this->distributionAnalyticsService->getAdminAnalytics();

        $quickActions = [
            // REMOVED: Product management moved to supplier dashboard
            // [
            //     'title' => 'Kelola Produk',
            //     'description' => 'Tambah dan kelola produk',
            //     'icon' => 'box',
            //     'route' => 'admin.products.index',
            //     'color' => 'green'
            // ],
            [
                'title' => 'Kelola Distribusi',
                'description' => 'Distribusi produk ke toko',
                'icon' => 'send',
                'route' => 'admin.distributions.index',
                'color' => 'purple'
            ],
            [
                'title' => 'Kelola Toko',
                'description' => 'Manajemen toko',
                'icon' => 'store',
                'route' => 'admin.stores',
                'color' => 'blue'
            ],
            [
                'title' => 'Kelola Supplier',
                'description' => 'Manajemen supplier',
                'icon' => 'supplier',
                'route' => 'admin.suppliers.index',
                'color' => 'green'
            ]
        ];

        return view('admin.dashboard', compact(
            'stats',
            'detailedStats',
            'monthlyStats',
            'recentActivities',
            'quickActions',
            'analytics'
        ));
    }
}
