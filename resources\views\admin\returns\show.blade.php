@extends('layouts.admin')

@section('title', 'Detail Retur - Indah Berkah Abadi')

@section('content')
<div class="space-y-6">
    <!-- Header with Navigation -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <a href="{{ route('admin.returns.index') }}" 
                       class="text-gray-600 hover:text-gray-800 transition-colors">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Detail Retur</h1>
                        <p class="text-gray-600 mt-1">Informasi lengkap retur produk</p>
                    </div>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($return->status === 'requested')
                    <button onclick="openApproveModal('{{ $return->id }}')" 
                            class="admin-dashboard-btn admin-dashboard-btn-success">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Setujui Retur
                    </button>
                    <button onclick="openRejectModal('{{ $return->id }}')" 
                            class="admin-dashboard-btn admin-dashboard-btn-danger">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Tolak Retur
                    </button>
                    @elseif($return->status === 'approved')
                    <button onclick="openCompleteModal('{{ $return->id }}')" 
                            class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Selesaikan Retur
                    </button>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Return Status Card -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Status Retur</h2>
                    <p class="text-sm text-gray-600 mt-1">ID: {{ $return->id }}</p>
                </div>
                <div class="flex items-center space-x-2">
                    @if($return->status === 'requested')
                        <span class="admin-dashboard-badge admin-dashboard-badge-warning">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Menunggu Persetujuan
                        </span>
                    @elseif($return->status === 'approved')
                        <span class="admin-dashboard-badge admin-dashboard-badge-success">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Disetujui
                        </span>
                    @elseif($return->status === 'rejected')
                        <span class="admin-dashboard-badge admin-dashboard-badge-danger">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Ditolak
                        </span>
                    @elseif($return->status === 'completed')
                        <span class="admin-dashboard-badge admin-dashboard-badge-info">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Selesai
                        </span>
                    @else
                        <span class="admin-dashboard-badge admin-dashboard-badge-secondary">
                            {{ ucfirst($return->status) }}
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Return Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h3 class="admin-dashboard-card-title">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Informasi Produk
                </h3>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div>
                        <label class="admin-dashboard-label">Nama Produk</label>
                        <p class="admin-dashboard-value">{{ $return->product->name ?? 'N/A' }}</p>
                    </div>
                    <div>
                        <label class="admin-dashboard-label">Jumlah Retur</label>
                        <p class="admin-dashboard-value">{{ number_format($return->quantity) }} unit</p>
                    </div>
                    <div>
                        <label class="admin-dashboard-label">Alasan Retur</label>
                        <p class="admin-dashboard-value">
                            @switch($return->reason)
                                @case('damaged')
                                    Produk Rusak
                                    @break
                                @case('expired')
                                    Kadaluarsa
                                    @break
                                @case('defective')
                                    Cacat Produksi
                                    @break
                                @case('overstock')
                                    Kelebihan Stok
                                    @break
                                @case('other')
                                    Lainnya
                                    @break
                                @default
                                    {{ ucfirst($return->reason) }}
                            @endswitch
                        </p>
                    </div>
                    @if($return->description)
                    <div>
                        <label class="admin-dashboard-label">Deskripsi Detail</label>
                        <p class="admin-dashboard-value">{{ $return->description }}</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Store & Supplier Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h3 class="admin-dashboard-card-title">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    Informasi Toko & Supplier
                </h3>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    @if($return->store)
                    <div>
                        <label class="admin-dashboard-label">Toko Asal</label>
                        <p class="admin-dashboard-value">{{ $return->store->name }}</p>
                        <p class="text-sm text-gray-500">{{ $return->store->location }}</p>
                    </div>
                    @endif
                    
                    @if($return->supplier)
                    <div>
                        <label class="admin-dashboard-label">Supplier Tujuan</label>
                        <p class="admin-dashboard-value">{{ $return->supplier->name }}</p>
                        @if($return->supplier->contact_person)
                        <p class="text-sm text-gray-500">{{ $return->supplier->contact_person }}</p>
                        @endif
                        @if($return->supplier->phone)
                        <p class="text-sm text-gray-500">{{ $return->supplier->phone }}</p>
                        @endif
                    </div>
                    @else
                    <div>
                        <label class="admin-dashboard-label">Tujuan Retur</label>
                        <p class="admin-dashboard-value">Gudang Pusat</p>
                        <p class="text-sm text-gray-500">Retur internal ke gudang</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline & User Information -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Timeline -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h3 class="admin-dashboard-card-title">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Timeline Retur
                </h3>
            </div>
            <div class="admin-dashboard-card-content">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Retur Dibuat</p>
                            <p class="text-sm text-gray-500">{{ $return->return_date ? $return->return_date->format('d M Y H:i') : 'N/A' }}</p>
                            @if($return->requestedBy)
                            <p class="text-xs text-gray-400">oleh {{ $return->requestedBy->name }}</p>
                            @endif
                        </div>
                    </div>

                    @if($return->approved_date)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Retur Disetujui</p>
                            <p class="text-sm text-gray-500">{{ $return->approved_date->format('d M Y H:i') }}</p>
                            @if($return->approvedBy)
                            <p class="text-xs text-gray-400">oleh {{ $return->approvedBy->name }}</p>
                            @endif
                        </div>
                    </div>
                    @endif

                    @if($return->completed_date)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Retur Selesai</p>
                            <p class="text-sm text-gray-500">{{ $return->completed_date->format('d M Y H:i') }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Admin Notes -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h3 class="admin-dashboard-card-title">
                    <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Catatan Admin
                </h3>
            </div>
            <div class="admin-dashboard-card-content">
                @if($return->admin_notes)
                    <p class="admin-dashboard-value">{{ $return->admin_notes }}</p>
                @else
                    <p class="text-gray-500 italic">Belum ada catatan admin</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Setujui Retur</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeApproveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Persetujuan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyetujui retur ini? Retur yang disetujui akan diproses lebih lanjut.
                        </p>
                        <div>
                            <label for="admin_notes" class="admin-dashboard-label">Catatan Admin (Opsional)</label>
                            <textarea id="admin_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-input"
                                      placeholder="Tambahkan catatan untuk retur ini..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()"
                        class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-success">
                    Setujui Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Tolak Retur</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeRejectModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="rejectForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penolakan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menolak retur ini? Retur yang ditolak tidak dapat diproses lebih lanjut.
                        </p>
                        <div>
                            <label for="reject_admin_notes" class="admin-dashboard-label">Alasan Penolakan <span class="text-red-500">*</span></label>
                            <textarea id="reject_admin_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-input"
                                      placeholder="Jelaskan alasan penolakan retur ini..." required></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeRejectModal()"
                        class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                    Tolak Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Complete Modal -->
<div id="completeModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Selesaikan Retur</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeCompleteModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="completeForm" method="POST">
            @csrf
            @method('PUT')
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penyelesaian</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyelesaikan retur ini? Retur yang selesai akan mengupdate stok produk.
                        </p>
                        <div>
                            <label for="complete_admin_notes" class="admin-dashboard-label">Catatan Penyelesaian (Opsional)</label>
                            <textarea id="complete_admin_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-input"
                                      placeholder="Tambahkan catatan penyelesaian..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeCompleteModal()"
                        class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Selesaikan Retur
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function openApproveModal(returnId) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');
    form.action = `/admin/returns/${returnId}/approve`;
    modal.classList.add('show');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('show');
}

function openRejectModal(returnId) {
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');
    form.action = `/admin/returns/${returnId}/reject`;
    modal.classList.add('show');
}

function closeRejectModal() {
    const modal = document.getElementById('rejectModal');
    modal.classList.remove('show');
}

function openCompleteModal(returnId) {
    const modal = document.getElementById('completeModal');
    const form = document.getElementById('completeForm');
    form.action = `/admin/returns/${returnId}/complete`;
    modal.classList.add('show');
}

function closeCompleteModal() {
    const modal = document.getElementById('completeModal');
    modal.classList.remove('show');
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const modals = ['approveModal', 'rejectModal', 'completeModal'];
    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (event.target === modal) {
            modal.classList.remove('show');
        }
    });
});
</script>
@endsection
