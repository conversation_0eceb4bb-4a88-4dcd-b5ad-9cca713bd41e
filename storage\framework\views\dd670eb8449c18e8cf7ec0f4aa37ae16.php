<?php $__env->startSection('title', 'Distribusi Produk - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Distribusi Produk'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Distribusi Produk</h1>
                    <p class="text-gray-600 mt-1">Manajemen distribusi dari gudang pusat ke 25 toko</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.distributions.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Buat Distribusi
                    </a>
                    <a href="<?php echo e(route('admin.distributions.export', request()->query())); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Time Period Filter -->
    <?php echo $__env->make('admin.components.time-period-filter', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Distribution Stats - Compact Version -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['total'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Total Distribusi</div>
                        <?php if(isset($stats['period_label'])): ?>
                            <div class="text-xs text-gray-500"><?php echo e($stats['period_label']); ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['confirmed'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Dikonfirmasi</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['unconfirmed'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Belum Dikonfirmasi</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['today'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Hari Ini</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.distributions.index')); ?>">
                <!-- Preserve time period parameter -->
                <?php if(request('period')): ?>
                    <input type="hidden" name="period" value="<?php echo e(request('period')); ?>">
                <?php endif; ?>
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cari</label>
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Nomor, toko, catatan..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Toko</label>
                        <select name="store" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Semua Toko</option>
                            <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($store->id); ?>" <?php echo e(request('store') == $store->id ? 'selected' : ''); ?>>
                                    <?php echo e($store->name); ?>

                                </option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Semua Status</option>
                            <option value="confirmed" <?php echo e(request('status') === 'confirmed' ? 'selected' : ''); ?>>Dikonfirmasi</option>
                            <option value="unconfirmed" <?php echo e(request('status') === 'unconfirmed' ? 'selected' : ''); ?>>Belum Dikonfirmasi</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Dari Tanggal</label>
                        <input type="date" name="date_from" value="<?php echo e($dateFrom ?? request('date_from')); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sampai Tanggal</label>
                        <input type="date" name="date_to" value="<?php echo e($dateTo ?? request('date_to')); ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span class="block sm:inline"><?php echo e(session('error')); ?></span>
        </div>
    <?php endif; ?>

    <!-- Distributions Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Distribusi</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Toko Tujuan</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Distribusi</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $distributions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $distribution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($distribution->product->name); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($distribution->store->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($distribution->store->location); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="font-medium text-gray-900"><?php echo e($distribution->quantity); ?></span>
                            </td>
                            <td class="px-6 py-4"><?php echo e($distribution->date_distributed->format('d/m/Y')); ?></td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($distribution->status_badge_class); ?>">
                                    <?php echo e($distribution->status_label); ?>

                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center gap-2">
                                    <a href="<?php echo e(route('admin.distributions.show', $distribution)); ?>" class="text-blue-600 hover:text-blue-800 text-sm font-medium">Lihat</a>
                                    <?php if(!$distribution->confirmed): ?>
                                        <a href="<?php echo e(route('admin.distributions.edit', $distribution)); ?>" class="text-green-600 hover:text-green-800 text-sm font-medium">Edit</a>
                                        <form action="<?php echo e(route('admin.distributions.destroy', $distribution)); ?>" method="POST" class="inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus distribusi ini?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium">Hapus</button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                Tidak ada distribusi yang ditemukan
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if($distributions->hasPages()): ?>
            <div class="mt-4">
                <?php echo e($distributions->links('pagination.admin-dashboard')); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/distributions/index.blade.php ENDPATH**/ ?>