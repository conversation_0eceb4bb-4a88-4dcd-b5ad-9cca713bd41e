<?php

namespace App\Services;

use App\Models\Product;
use App\Models\User;
use App\Models\Distribution;
use Illuminate\Support\Facades\Schema;

class DashboardStatsService
{
    /**
     * Get dashboard statistics
     */
    public function getStats(): array
    {
        return [
            'total_stock' => $this->getTotalStock(),
            'active_stores' => $this->getActiveStores(),
            'pending_shipments' => $this->getPendingShipments(),
            'low_stock_items' => $this->getLowStockItems(),
        ];
    }

    /**
     * Get total stock across all products
     */
    private function getTotalStock(): int
    {
        try {
            $warehouseStock = Schema::hasTable('warehouse_stock') ?
                \App\Models\WarehouseStock::sum('quantity') : 0;
            $storeStock = Schema::hasTable('store_stock') ?
                \App\Models\StoreStock::sum('quantity') : 0;

            return $warehouseStock + $storeStock;
        } catch (\Exception $e) {
            \Log::warning('Error calculating total stock: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get count of active stores (stores with assigned users)
     */
    private function getActiveStores(): int
    {
        try {
            // Count stores that have users assigned (active stores)
            // This excludes central warehouse and other stores without staff
            return Schema::hasTable('users') && Schema::hasTable('stores') ?
                \App\Models\User::where('role', 'user')->whereNotNull('store_id')->count() : 0;
        } catch (\Exception $e) {
            \Log::warning('Error counting active stores: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get count of pending shipments
     */
    private function getPendingShipments(): int
    {
        try {
            return Schema::hasTable('distributions') ?
                Distribution::where('confirmed', false)->count() : 0;
        } catch (\Exception $e) {
            \Log::warning('Error counting pending shipments: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get count of low stock items
     */
    private function getLowStockItems(): int
    {
        try {
            // For simplified structure, count products with low warehouse stock
            return Schema::hasTable('warehouse_stock') ?
                \App\Models\WarehouseStock::where('quantity', '<', 10)->count() : 0;
        } catch (\Exception $e) {
            \Log::warning('Error counting low stock items: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get detailed stock statistics
     */
    public function getDetailedStats(): array
    {
        try {
            $totalProducts = Schema::hasTable('products') ? Product::count() : 0;
            $warehouseStock = Schema::hasTable('warehouse_stock') ?
                \App\Models\WarehouseStock::sum('quantity') : 0;
            $storeStock = Schema::hasTable('store_stock') ?
                \App\Models\StoreStock::sum('quantity') : 0;
            $totalDistributions = Schema::hasTable('distributions') ?
                \App\Models\Distribution::count() : 0;
            $confirmedDistributions = Schema::hasTable('distributions') ?
                \App\Models\Distribution::where('confirmed', true)->count() : 0;

            return [
                'total_products' => $totalProducts,
                'warehouse_stock' => $warehouseStock,
                'store_stock' => $storeStock,
                'total_distributions' => $totalDistributions,
                'confirmed_distributions' => $confirmedDistributions,
            ];
        } catch (\Exception $e) {
            \Log::warning('Error getting detailed stats: ' . $e->getMessage());
            return [
                'total_products' => 0,
                'warehouse_stock' => 0,
                'store_stock' => 0,
                'total_distributions' => 0,
                'confirmed_distributions' => 0,
            ];
        }
    }

    /**
     * Get monthly statistics
     */
    public function getMonthlyStats(): array
    {
        try {
            $currentMonth = now()->startOfMonth();

            $warehouseStock = Schema::hasTable('warehouse_stock') ?
                \App\Models\WarehouseStock::sum('quantity') : 0;
            $storeStock = Schema::hasTable('store_stock') ?
                \App\Models\StoreStock::sum('quantity') : 0;
            $distributions = Schema::hasTable('distributions') ?
                Distribution::where('created_at', '>=', $currentMonth)->count() : 0;
            $completedDistributions = Schema::hasTable('distributions') ?
                Distribution::where('confirmed', true)
                    ->where('updated_at', '>=', $currentMonth)
                    ->count() : 0;

            return [
                'warehouse_stock' => $warehouseStock,
                'store_stock' => $storeStock,
                'distributions' => $distributions,
                'completed_distributions' => $completedDistributions,
            ];
        } catch (\Exception $e) {
            \Log::warning('Error getting monthly stats: ' . $e->getMessage());
            return [
                'warehouse_stock' => 0,
                'store_stock' => 0,
                'distributions' => 0,
                'completed_distributions' => 0,
            ];
        }
    }
}
