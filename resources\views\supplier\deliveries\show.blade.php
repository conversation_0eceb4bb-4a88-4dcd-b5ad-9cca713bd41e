@extends('layouts.supplier')

@section('title', 'Detail <PERSON>man - Dashboard Supplier')
@section('page-title', 'Detail Pengiriman')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Detail Pengiriman</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap pengiriman produk ke gudang</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if($delivery->status === 'pending')
                    <button onclick="openDeleteModal('{{ $delivery->id }}', '{{ $delivery->product->name }}')"
                            class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Hapus Pengiriman
                    </button>
                    @endif
                    <a href="{{ route('supplier.deliveries.index') }}" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery Status Card -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center space-x-4">
                    <div class="supplier-dashboard-status-icon supplier-dashboard-status-{{ $delivery->status }}">
                        @if($delivery->status === 'pending')
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        @elseif($delivery->status === 'received')
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        @elseif($delivery->status === 'partial')
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        @elseif($delivery->status === 'cancelled')
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        @endif
                    </div>
                    <div>
                        <h2 class="text-lg font-semibold text-gray-900">
                            Status: 
                            <span class="supplier-dashboard-status-badge supplier-dashboard-status-{{ $delivery->status }}">
                                @if($delivery->status === 'pending') Menunggu Penerimaan
                                @elseif($delivery->status === 'received') Diterima Lengkap
                                @elseif($delivery->status === 'partial') Diterima Sebagian
                                @elseif($delivery->status === 'cancelled') Dibatalkan
                                @else {{ ucfirst($delivery->status) }}
                                @endif
                            </span>
                        </h2>
                        <p class="text-sm text-gray-600 mt-1">ID Pengiriman: {{ $delivery->id }}</p>
                    </div>
                </div>
                
                @if($delivery->status !== 'pending' && $delivery->status !== 'cancelled')
                <div class="supplier-dashboard-progress-container">
                    <div class="text-sm text-gray-600 mb-1">Progress Penerimaan</div>
                    <div class="supplier-dashboard-progress-bar">
                        <div class="supplier-dashboard-progress-fill" style="width: {{ $delivery->completion_percentage }}%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">{{ $delivery->completion_percentage }}% selesai</div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Delivery Information -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Informasi Pengiriman</h3>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <!-- Delivery Date -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Tanggal Pengiriman
                        </div>
                        <div class="supplier-dashboard-info-value">{{ auth()->user()->formatDate($delivery->delivery_date) }}</div>
                    </div>

                    <!-- Received Date -->
                    @if($delivery->received_date)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Tanggal Diterima
                        </div>
                        <div class="supplier-dashboard-info-value">{{ auth()->user()->formatDate($delivery->received_date) }}</div>
                    </div>
                    @endif

                    <!-- Supplier -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Supplier
                        </div>
                        <div class="supplier-dashboard-info-value">
                            <div class="font-medium">{{ $delivery->supplier->name }}</div>
                            @if($delivery->supplier->contact_person)
                            <div class="text-sm text-gray-500">{{ $delivery->supplier->contact_person }}</div>
                            @endif
                        </div>
                    </div>

                    <!-- Received By -->
                    @if($delivery->receivedBy)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Diterima Oleh
                        </div>
                        <div class="supplier-dashboard-info-value">
                            <div class="font-medium">{{ $delivery->receivedBy->name }}</div>
                            <div class="text-sm text-gray-500">Admin Gudang</div>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Product Information -->
        <div class="supplier-dashboard-card">
            <div class="supplier-dashboard-card-header">
                <h3 class="supplier-dashboard-card-title">Informasi Produk</h3>
            </div>
            <div class="supplier-dashboard-card-content">
                <div class="space-y-4">
                    <!-- Product Name -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Nama Produk
                        </div>
                        <div class="supplier-dashboard-info-value font-medium">{{ $delivery->product->name }}</div>
                    </div>

                    <!-- Quantity -->
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                            </svg>
                            Jumlah Dikirim
                        </div>
                        <div class="supplier-dashboard-info-value font-medium">{{ number_format($delivery->quantity) }} unit</div>
                    </div>

                    <!-- Received Quantity -->
                    @if($delivery->received_quantity)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Jumlah Diterima
                        </div>
                        <div class="supplier-dashboard-info-value font-medium text-green-600">{{ number_format($delivery->received_quantity) }} unit</div>
                    </div>
                    @endif

                    <!-- Remaining Quantity -->
                    @if($delivery->remaining_quantity > 0)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Sisa Belum Diterima
                        </div>
                        <div class="supplier-dashboard-info-value font-medium text-orange-600">{{ number_format($delivery->remaining_quantity) }} unit</div>
                    </div>
                    @endif

                    <!-- Unit Price -->
                    @if($delivery->unit_price)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Harga Satuan
                        </div>
                        <div class="supplier-dashboard-info-value">Rp {{ number_format($delivery->unit_price, 2, ',', '.') }}</div>
                    </div>
                    @endif

                    <!-- Total Price -->
                    @if($delivery->total_price)
                    <div class="supplier-dashboard-info-row">
                        <div class="supplier-dashboard-info-label">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            Total Harga
                        </div>
                        <div class="supplier-dashboard-info-value font-semibold text-blue-600">Rp {{ number_format($delivery->total_price, 2, ',', '.') }}</div>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Notes Section -->
    @if($delivery->notes)
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h3 class="supplier-dashboard-card-title">Catatan Pengiriman</h3>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="supplier-dashboard-notes-content">
                <svg class="w-5 h-5 text-gray-400 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                </svg>
                <p class="text-gray-700 leading-relaxed">{{ $delivery->notes }}</p>
            </div>
        </div>
    </div>
    @endif
</div>
<!-- Deletion Confirmation Modal -->
<div id="deleteModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Konfirmasi Hapus Pengiriman</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <div class="supplier-dashboard-modal-body">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Hapus Pengiriman</h4>
                    <p class="text-sm text-gray-600 mb-4">
                        Apakah Anda yakin ingin menghapus pengiriman produk <strong>{{ $delivery->product->name }}</strong>?
                    </p>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="text-sm text-yellow-800">
                                <p class="font-medium">Perhatian:</p>
                                <p>Pengiriman yang sudah diterima oleh admin gudang tidak dapat dihapus. Hanya pengiriman dengan status "Menunggu" yang dapat dihapus.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="supplier-dashboard-modal-footer">
            <button type="button" onclick="closeDeleteModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                Batal
            </button>
            <form method="POST" action="{{ route('supplier.deliveries.destroy', $delivery) }}" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Ya, Hapus Pengiriman
                </button>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function openDeleteModal(deliveryId, productName) {
    const modal = document.getElementById('deleteModal');
    modal.classList.add('active');
}

function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    modal.classList.remove('active');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDeleteModal();
    }
});
</script>
@endpush

@endsection
