<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\User;
use Carbon\Carbon;
use App\Traits\SupplierHelper;

class SupplierDeliveryController extends Controller
{
    use SupplierHelper;
    /**
     * Display a listing of supplier deliveries.
     */
    public function index(Request $request)
    {
        $query = SupplierDelivery::with(['supplier', 'product', 'receivedBy']);
        
        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);
        
        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();
        
        // Apply date filter
        $query->whereBetween('delivery_date', [$startDate, $endDate]);
        
        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->whereHas('supplier', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            })->orWhereHas('product', function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }
        
        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }
        
        // Sort by
        $sortBy = $request->get('sort_by', 'delivery_date');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['delivery_date', 'received_date', 'quantity', 'status'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('delivery_date', 'desc');
        }
        
        $deliveries = $query->paginate(15);
        
        // Get statistics
        $stats = [
            'total' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'pending' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'pending')->count(),
            'received' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'received')->count(),
            'partial' => SupplierDelivery::whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'partial')->count(),
        ];
        
        return view('supplier.deliveries.index', compact('deliveries', 'stats', 'filterMonth'));
    }
    
    /**
     * Show the form for creating a new delivery.
     */
    public function create()
    {
        // Get warehouse admin users (admin role) to deliver TO
        $warehouseAdmins = User::where('role', 'admin')->orderBy('name')->get();
        $products = Product::orderBy('name')->get();

        return view('supplier.deliveries.create', compact('warehouseAdmins', 'products'));
    }
    
    /**
     * Store a newly created delivery in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'warehouse_admin_id' => 'required|exists:users,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'nullable|numeric|min:0',
            'delivery_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
        ], [
            'warehouse_admin_id.required' => 'Admin gudang wajib dipilih',
            'warehouse_admin_id.exists' => 'Admin gudang tidak valid',
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'unit_price.numeric' => 'Harga satuan harus berupa angka',
            'unit_price.min' => 'Harga satuan tidak boleh negatif',
            'delivery_date.required' => 'Tanggal pengiriman wajib diisi',
            'delivery_date.date' => 'Format tanggal tidak valid',
            'notes.max' => 'Catatan maksimal 1000 karakter',
        ]);

        // Calculate total price if unit price is provided
        if ($validatedData['unit_price']) {
            $validatedData['total_price'] = $validatedData['unit_price'] * $validatedData['quantity'];
        }

        // Get the current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        $validatedData['supplier_id'] = $supplier->id;
        unset($validatedData['warehouse_admin_id']); // Remove this as it's not in the SupplierDelivery model

        $delivery = SupplierDelivery::create($validatedData);

        return redirect()->route('supplier.deliveries.index')
            ->with('success', 'Pengiriman berhasil ditambahkan');
    }

    /**
     * Create a new product via AJAX from supplier delivery form.
     */
    public function createProduct(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255|unique:products,name',
        ], [
            'name.required' => 'Nama produk wajib diisi',
            'name.string' => 'Nama produk harus berupa teks',
            'name.max' => 'Nama produk maksimal 255 karakter',
            'name.unique' => 'Nama produk sudah digunakan. Silakan gunakan nama yang berbeda',
        ]);

        // Create the product (no initial stock for supplier-created products)
        $product = Product::create([
            'name' => $validatedData['name']
        ]);

        // Return JSON response for AJAX
        return response()->json([
            'success' => true,
            'product' => [
                'id' => $product->id,
                'name' => $product->name
            ],
            'message' => 'Produk berhasil dibuat: ' . $product->name
        ]);
    }

    /**
     * Display the specified delivery.
     */
    public function show(SupplierDelivery $delivery)
    {
        $delivery->load(['supplier', 'product', 'receivedBy']);
        
        return view('supplier.deliveries.show', compact('delivery'));
    }
    

    /**
     * Remove the specified delivery from storage.
     */
    public function destroy(SupplierDelivery $delivery)
    {
        // Only allow deletion if delivery is still pending
        if ($delivery->status !== 'pending') {
            return redirect()->route('supplier.deliveries.index')
                ->with('error', 'Pengiriman yang sudah diterima tidak dapat dihapus');
        }
        
        $delivery->delete();
        
        return redirect()->route('supplier.deliveries.index')
            ->with('success', 'Pengiriman berhasil dihapus');
    }
}
