/* Indah Berkah Abadi - Supplier Dashboard Mobile Z-Index Fixes */
/* Independent CSS for mobile accessibility and z-index management */
/*
 * COMPREHENSIVE Z-INDEX HIERARCHY FOR MOBILE ACCESSIBILITY:
 * - Sidebar navigation: 1000+ (highest priority)
 * - Modal overlays: 500-999 (high priority, below sidebar)
 * - Dropdown elements: 100-499 (medium-high priority)
 * - Interactive form elements: 50-99 (medium priority)
 * - Buttons and clickable elements: 10-49 (low-medium priority)
 * - Hover states and tooltips: 2-9 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== MOBILE-SPECIFIC Z-INDEX FIXES ===== */

/* Apply fixes only on mobile devices */
@media (max-width: 768px) {
    /* Sidebar - highest priority */
    .supplier-dashboard-sidebar {
        z-index: 1000 !important;
        position: fixed !important;
        pointer-events: auto !important;
    }

    /* Sidebar overlay - just below sidebar */
    .supplier-dashboard-sidebar-overlay {
        z-index: 999 !important;
        position: fixed !important;
        pointer-events: auto !important;
    }

    /* Sidebar when closed - prevent interference */
    .supplier-dashboard-sidebar:not(.supplier-dashboard-sidebar-open) {
        pointer-events: none !important;
    }

    /* Sidebar when open - allow interaction */
    .supplier-dashboard-sidebar.supplier-dashboard-sidebar-open {
        pointer-events: auto !important;
    }

    /* Header - ensure it's above main content but below sidebar */
    .supplier-dashboard-header {
        z-index: 200 !important;
        position: sticky !important;
        pointer-events: auto !important;
    }

    /* Header left section - contains mobile menu button */
    .supplier-dashboard-header-left {
        z-index: 201 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Mobile hamburger menu button - ensure it's always clickable */
    .supplier-dashboard-mobile-menu-btn {
        z-index: 202 !important;
        position: relative !important;
        pointer-events: auto !important;
        cursor: pointer !important;
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
        min-height: 44px !important;
        min-width: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Ensure all child elements of menu button are clickable */
    .supplier-dashboard-mobile-menu-btn * {
        pointer-events: none !important;
    }

    /* Header dropdowns - high priority but below sidebar */
    .supplier-dashboard-user-dropdown-menu {
        z-index: 600 !important;
        position: absolute !important;
        pointer-events: auto !important;
    }

    /* Header user dropdown container */
    .supplier-dashboard-user-dropdown {
        z-index: 150 !important;
        position: relative !important;
    }

    /* Header user dropdown button */
    .supplier-dashboard-user-dropdown-btn {
        z-index: 151 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
    }

    /* Sidebar user menu dropdown - highest priority within sidebar */
    .supplier-dashboard-user-menu-dropdown {
        z-index: 1100 !important;
        position: absolute !important;
        pointer-events: auto !important;
    }

    /* Sidebar user menu button */
    .supplier-dashboard-user-menu-button {
        z-index: 1050 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
    }

    /* Modal overlays - high priority but below sidebar */
    .supplier-dashboard-modal-overlay,
    .supplier-dashboard-confirmation-modal-overlay {
        z-index: 800 !important;
        position: fixed !important;
        pointer-events: auto !important;
    }

    /* Modal content */
    .supplier-dashboard-modal,
    .supplier-dashboard-confirmation-modal {
        z-index: 801 !important;
        position: fixed !important;
        pointer-events: auto !important;
    }

    /* Enhanced form elements - highest interactive priority */
    .supplier-dashboard-form-select-enhanced,
    .supplier-dashboard-form-group-enhanced .supplier-dashboard-form-select {
        z-index: 80 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Enhanced form elements when focused */
    .supplier-dashboard-form-select-enhanced:focus,
    .supplier-dashboard-form-group-enhanced .supplier-dashboard-form-select:focus {
        z-index: 90 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Enhanced containers */
    .supplier-dashboard-dropdown-container-enhanced,
    .supplier-dashboard-filter-group-enhanced,
    .supplier-dashboard-form-group-enhanced {
        z-index: 75 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Header action buttons */
    .supplier-dashboard-header-actions .supplier-dashboard-btn,
    .supplier-dashboard-header-actions .supplier-dashboard-btn-sm,
    .supplier-dashboard-header-actions a {
        z-index: 50 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
    }

    /* Card action buttons */
    .supplier-dashboard-card-actions .supplier-dashboard-btn,
    .supplier-dashboard-card-actions .supplier-dashboard-btn-sm,
    .supplier-dashboard-card-actions a {
        z-index: 45 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
    }

    /* General buttons - ensure they're always clickable */
    .supplier-dashboard-btn,
    .supplier-dashboard-btn-primary,
    .supplier-dashboard-btn-secondary,
    .supplier-dashboard-btn-sm,
    .supplier-dashboard-btn-danger {
        z-index: 40 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Form inputs and selects */
    .supplier-dashboard-input,
    .supplier-dashboard-select,
    .supplier-dashboard-textarea,
    .supplier-dashboard-form-input,
    .supplier-dashboard-form-select,
    .supplier-dashboard-form-textarea {
        z-index: 30 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
    }

    /* Pagination buttons */
    .supplier-dashboard-pagination-btn {
        z-index: 35 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
        min-width: 44px !important;
    }

    /* Badge elements */
    .supplier-dashboard-badge {
        z-index: 20 !important;
        position: relative !important;
    }

    /* Navigation items in sidebar */
    .supplier-dashboard-nav-item {
        z-index: 1010 !important;
        position: relative !important;
        pointer-events: auto !important;
        min-height: 44px !important;
        display: flex !important;
        align-items: center !important;
    }

    /* Main content */
    .supplier-dashboard-main {
        z-index: 1 !important;
        position: relative !important;
    }

    /* Content area */
    .supplier-dashboard-content {
        z-index: 1 !important;
        position: relative !important;
    }
}

/* ===== MOBILE LAYOUT FIXES ===== */

@media (max-width: 768px) {
    /* Ensure proper body scroll behavior */
    body.supplier-sidebar-open {
        overflow: hidden;
    }

    /* Fix sidebar width on small screens */
    .supplier-dashboard-sidebar {
        width: calc(100vw - 2rem);
        max-width: 280px;
    }

    /* Ensure touch targets meet accessibility requirements */
    .supplier-dashboard-nav-item,
    .supplier-dashboard-user-menu-button,
    .supplier-dashboard-dropdown-item,
    .supplier-dashboard-mobile-menu-btn {
        min-height: 44px;
        display: flex;
        align-items: center;
    }

    /* Improve touch interaction for mobile menu button */
    .supplier-dashboard-mobile-menu-btn {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }

    /* Prevent text selection on interactive elements */
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-nav-item,
    .supplier-dashboard-user-menu-button {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

@media (max-width: 768px) {
    /* Focus states for better accessibility */
    .supplier-dashboard-mobile-menu-btn:focus,
    .supplier-dashboard-nav-item:focus,
    .supplier-dashboard-btn:focus {
        outline: 2px solid #3b82f6;
        outline-offset: 2px;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .supplier-dashboard-sidebar {
            border-right: 2px solid #000;
        }

        .supplier-dashboard-mobile-menu-btn {
            border: 2px solid #000;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .supplier-dashboard-sidebar,
        .supplier-dashboard-sidebar-overlay,
        .supplier-dashboard-mobile-menu-btn {
            transition: none;
        }
    }
}

/* ===== EXTRA SMALL MOBILE FIXES ===== */

@media (max-width: 375px) {
    .supplier-dashboard-sidebar {
        width: calc(100vw - 1rem);
        max-width: none;
    }

    .supplier-dashboard-content {
        padding: 0.5rem;
    }

    .supplier-dashboard-header {
        padding: 0.75rem;
    }
}

/* ===== LANDSCAPE MOBILE FIXES ===== */

@media (max-width: 768px) and (orientation: landscape) {
    .supplier-dashboard-sidebar {
        width: 280px;
        max-width: 50vw;
    }
}

/* ===== DEBUGGING HELPERS (Remove in production) ===== */

/* Uncomment for debugging z-index issues */
/*
@media (max-width: 768px) {
    .supplier-dashboard-sidebar {
        border: 2px solid red !important;
    }

    .supplier-dashboard-mobile-menu-btn {
        border: 2px solid blue !important;
        background: rgba(0, 0, 255, 0.3) !important;
    }

    .supplier-dashboard-sidebar-overlay {
        border: 2px solid green !important;
    }

    .supplier-dashboard-header-left {
        border: 2px solid orange !important;
    }
}
*/

/* ===== FINAL MOBILE ACCESSIBILITY FIXES ===== */

@media (max-width: 768px) {
    /* Ensure all interactive elements are properly layered */
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-mobile-menu-btn * {
        pointer-events: auto !important;
        z-index: 202 !important;
    }

    /* Fix any potential conflicts with overlapping elements */
    .supplier-dashboard-header-right {
        z-index: 180 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Ensure dropdown menus work properly */
    .supplier-dashboard-dropdown-show {
        z-index: 700 !important;
        position: absolute !important;
        pointer-events: auto !important;
    }

    .supplier-dashboard-user-menu-dropdown-show {
        z-index: 1100 !important;
        position: absolute !important;
        pointer-events: auto !important;
    }

    /* Fix table interactions on mobile */
    .supplier-dashboard-table-container {
        z-index: 10 !important;
        position: relative !important;
    }

    .supplier-dashboard-table {
        z-index: 11 !important;
        position: relative !important;
    }

    /* Ensure form elements in tables work */
    .supplier-dashboard-table .supplier-dashboard-btn,
    .supplier-dashboard-table .supplier-dashboard-form-input,
    .supplier-dashboard-table .supplier-dashboard-form-select {
        z-index: 50 !important;
        position: relative !important;
        pointer-events: auto !important;
    }

    /* Fix any issues with cards */
    .supplier-dashboard-card {
        z-index: 5 !important;
        position: relative !important;
    }

    /* Ensure all clickable elements have proper touch targets */
    .supplier-dashboard-btn,
    .supplier-dashboard-nav-item,
    .supplier-dashboard-dropdown-item,
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-user-menu-button,
    .supplier-dashboard-user-dropdown-btn {
        min-height: 44px !important;
        min-width: 44px !important;
        touch-action: manipulation !important;
        -webkit-tap-highlight-color: transparent !important;
    }

    /* Prevent text selection on interactive elements */
    .supplier-dashboard-mobile-menu-btn,
    .supplier-dashboard-nav-item,
    .supplier-dashboard-user-menu-button,
    .supplier-dashboard-user-dropdown-btn {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Ensure focus states are visible */
    .supplier-dashboard-mobile-menu-btn:focus,
    .supplier-dashboard-nav-item:focus,
    .supplier-dashboard-btn:focus,
    .supplier-dashboard-form-input:focus,
    .supplier-dashboard-form-select:focus {
        outline: 2px solid #3b82f6 !important;
        outline-offset: 2px !important;
        z-index: 999 !important;
    }
}
