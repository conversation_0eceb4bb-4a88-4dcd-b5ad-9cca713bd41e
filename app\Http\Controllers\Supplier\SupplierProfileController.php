<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use App\Services\TimezoneService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class SupplierProfileController extends Controller
{
    /**
     * Display the supplier profile.
     */
    public function show()
    {
        $user = auth()->user();
        
        return view('supplier.profile.show', compact('user'));
    }
    
    /**
     * Show the form for editing the supplier profile.
     */
    public function edit()
    {
        $user = auth()->user();
        $timezones = TimezoneService::getAvailableTimezones();

        return view('supplier.profile.edit', compact('user', 'timezones'));
    }
    
    /**
     * Update the supplier profile.
     */
    public function update(Request $request)
    {
        $user = auth()->user();

        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'timezone' => 'required|in:Asia/Jakarta,Asia/Makassar,Asia/Jayapura',
        ], [
            'name.required' => 'Nama wajib diisi',
            'name.string' => 'Nama harus berupa teks',
            'name.max' => 'Nama maksimal 255 karakter',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah digunakan',
            'timezone.required' => 'Zona waktu wajib dipilih',
            'timezone.in' => 'Zona waktu tidak valid',
        ]);

        $user->update($validatedData);

        return redirect()->route('supplier.profile.show')
            ->with('success', 'Profil berhasil diperbarui');
    }

    /**
     * Show password change form
     */
    public function showPasswordForm()
    {
        return view('supplier.profile.password');
    }

    /**
     * Update supplier password (simplified - no current password required)
     */
    public function updatePassword(Request $request)
    {
        $request->validate([
            'password' => ['required', 'confirmed', Password::defaults()],
        ], [
            'password.required' => 'Kata sandi baru wajib diisi',
            'password.confirmed' => 'Konfirmasi kata sandi tidak cocok',
        ]);

        auth()->user()->update([
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('supplier.profile.show')
            ->with('success', 'Kata sandi berhasil diperbarui');
    }
}
