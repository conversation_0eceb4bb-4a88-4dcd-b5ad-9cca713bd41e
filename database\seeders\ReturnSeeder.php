<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ReturnModel;
use App\Models\Product;
use App\Models\Store;
use App\Models\Supplier;
use App\Models\User;
use Carbon\Carbon;

class ReturnSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get required data
        $products = Product::all();
        $stores = Store::where('name', '!=', 'Gudang Pusat')->get();
        $suppliers = Supplier::where('status', 'active')->get();
        $admin = User::where('role', 'admin')->first();
        $storeUser = User::where('role', 'user')->first();

        if ($products->count() > 0 && $stores->count() > 0 && $suppliers->count() > 0 && $admin && $storeUser) {
            // Create sample returns with different statuses and scenarios
            $returns = [
                [
                    'product_id' => $products->first()->id,
                    'store_id' => $stores->first()->id,
                    'supplier_id' => $suppliers->first()->id,
                    'quantity' => 10,
                    'reason' => 'damaged',
                    'description' => 'Produk rusak akibat kemasan bocor selama pengiriman',
                    'status' => 'requested',
                    'return_date' => Carbon::now()->subDays(2),
                    'requested_by' => $storeUser->id,
                ],
                [
                    'product_id' => $products->skip(1)->first()->id,
                    'store_id' => $stores->first()->id,
                    'supplier_id' => $suppliers->skip(1)->first()->id,
                    'quantity' => 5,
                    'reason' => 'expired',
                    'description' => 'Produk mendekati tanggal kadaluarsa',
                    'status' => 'approved',
                    'return_date' => Carbon::now()->subDays(5),
                    'approved_date' => Carbon::now()->subDays(3),
                    'requested_by' => $storeUser->id,
                    'approved_by' => $admin->id,
                    'admin_notes' => 'Retur disetujui, produk akan dikembalikan ke supplier',
                ],
                [
                    'product_id' => $products->skip(2)->first()->id,
                    'store_id' => $stores->first()->id,
                    'supplier_id' => $suppliers->first()->id,
                    'quantity' => 15,
                    'reason' => 'defective',
                    'description' => 'Produk cacat produksi, tidak berfungsi dengan baik',
                    'status' => 'completed',
                    'return_date' => Carbon::now()->subDays(10),
                    'approved_date' => Carbon::now()->subDays(8),
                    'completed_date' => Carbon::now()->subDays(6),
                    'requested_by' => $storeUser->id,
                    'approved_by' => $admin->id,
                    'admin_notes' => 'Retur selesai, produk telah dikembalikan ke supplier dan stok telah disesuaikan',
                ],
                [
                    'product_id' => $products->skip(3)->first()->id,
                    'store_id' => $stores->first()->id,
                    'supplier_id' => null, // Return to warehouse
                    'quantity' => 8,
                    'reason' => 'overstock',
                    'description' => 'Kelebihan stok, perlu dikembalikan ke gudang pusat',
                    'status' => 'approved',
                    'return_date' => Carbon::now()->subDays(3),
                    'approved_date' => Carbon::now()->subDays(1),
                    'requested_by' => $storeUser->id,
                    'approved_by' => $admin->id,
                    'admin_notes' => 'Retur ke gudang pusat disetujui',
                ],
                [
                    'product_id' => $products->first()->id,
                    'store_id' => $stores->first()->id,
                    'supplier_id' => $suppliers->skip(1)->first()->id,
                    'quantity' => 3,
                    'reason' => 'other',
                    'description' => 'Produk tidak sesuai dengan pesanan yang diminta',
                    'status' => 'rejected',
                    'return_date' => Carbon::now()->subDays(7),
                    'approved_date' => Carbon::now()->subDays(5),
                    'requested_by' => $storeUser->id,
                    'approved_by' => $admin->id,
                    'admin_notes' => 'Retur ditolak karena produk masih dalam kondisi baik dan sesuai spesifikasi',
                ],
            ];

            foreach ($returns as $returnData) {
                ReturnModel::firstOrCreate(
                    [
                        'product_id' => $returnData['product_id'],
                        'store_id' => $returnData['store_id'],
                        'return_date' => $returnData['return_date'],
                    ],
                    $returnData
                );
            }

            $this->command->info('Created sample return data');
        } else {
            $this->command->warn('Insufficient data to create returns. Make sure products, stores, suppliers, and users exist.');
        }
    }
}
