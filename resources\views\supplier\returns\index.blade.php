@extends('layouts.supplier')

@section('title', 'Pengiriman yang <PERSON> - Dashboard Supplier')
@section('page-title', 'Pengiriman yang Dibatalk<PERSON>')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Pengiriman yang Dibatalkan</h1>
                    <p class="text-gray-600 mt-1">Kelola pengiriman yang dibatalkan dan kirim ulang jika diperlukan</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['cancelled_deliveries']) }}</div>
            <div class="supplier-dashboard-stat-label">Pengiriman Dibatalkan</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['total_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['requested_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Retur Diminta</div>
        </div>

    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month"
                           name="month"
                           value="{{ $filterMonth }}"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari produk..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Cancelled Deliveries Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Pengiriman yang Dibatalkan</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Harga Satuan</th>
                            <th class="px-6 py-3">Total Harga</th>
                            <th class="px-6 py-3">Tanggal Pengiriman</th>
                            <th class="px-6 py-3">Catatan</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cancelledDeliveries as $delivery)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->product->name ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($delivery->quantity) }} unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp {{ number_format($delivery->unit_price, 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp {{ number_format($delivery->total_price, 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->delivery_date ? $delivery->delivery_date->format('d M Y') : 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $delivery->notes ?? 'Tidak ada catatan' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="openDeleteCancelledModal('{{ $delivery->id }}', '{{ $delivery->product->name ?? 'N/A' }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-danger text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus
                                    </button>
                                    <button onclick="openResendModal('{{ $delivery->id }}', '{{ $delivery->product->name ?? 'N/A' }}', '{{ $delivery->quantity }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Kirim Ulang
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman yang dibatalkan</p>
                                    <p>Pengiriman yang dibatalkan akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>

<!-- Response Modal -->
<div id="responseModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Respons Retur</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeRespondModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="responseForm" method="POST">
            @csrf
            @method('PUT')
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Aksi</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="action" value="accept" class="mr-2" required>
                                <span class="text-green-600 font-medium">Terima Retur</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="action" value="reject" class="mr-2" required>
                                <span class="text-red-600 font-medium">Tolak Retur</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label for="supplier_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="supplier_notes"
                                  name="supplier_notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Tambahkan catatan untuk respons ini..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeRespondModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Kirim Respons
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Resend Cancelled Delivery Modal -->
<div id="resendModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Kirim Ulang Pengiriman</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeResendModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="resendForm" method="POST">
            @csrf
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div>
                        <p class="text-sm text-gray-600 mb-4">
                            Buat pengiriman baru berdasarkan pengiriman yang dibatalkan untuk produk: <strong id="resendProductName"></strong>
                        </p>
                        <p class="text-sm text-gray-600 mb-4">
                            Jumlah: <strong id="resendQuantity"></strong> unit
                        </p>
                    </div>

                    <div>
                        <label for="resend_delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman Baru <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="resend_delivery_date"
                               name="delivery_date"
                               min="{{ date('Y-m-d') }}"
                               class="supplier-dashboard-input"
                               required>
                    </div>

                    <div>
                        <label for="resend_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="resend_notes"
                                  name="notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Catatan untuk pengiriman ulang..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeResendModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Buat Pengiriman Baru
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Cancelled Delivery Modal -->
<div id="deleteCancelledModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Hapus Pengiriman yang Dibatalkan</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteCancelledModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="deleteCancelledForm" method="POST">
            @csrf
            @method('DELETE')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penghapusan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menghapus pengiriman yang dibatalkan untuk produk <strong id="deleteProductName"></strong>?
                            Tindakan ini tidak dapat dibatalkan.
                        </p>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeDeleteCancelledModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    Ya, Hapus Pengiriman
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
function openRespondModal(returnId) {
    const modal = document.getElementById('responseModal');
    const form = document.getElementById('responseForm');
    form.action = `/supplier/returns/${returnId}/respond`;
    modal.classList.add('active');
}

function closeRespondModal() {
    const modal = document.getElementById('responseModal');
    modal.classList.remove('active');
    document.getElementById('responseForm').reset();
}

function openResendModal(deliveryId, productName, quantity) {
    const modal = document.getElementById('resendModal');
    const form = document.getElementById('resendForm');
    const productNameElement = document.getElementById('resendProductName');
    const quantityElement = document.getElementById('resendQuantity');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function closeResendModal() {
    const modal = document.getElementById('resendModal');
    modal.classList.remove('active');
    document.getElementById('resendForm').reset();
}

function openDeleteCancelledModal(deliveryId, productName) {
    const modal = document.getElementById('deleteCancelledModal');
    const form = document.getElementById('deleteCancelledForm');
    const productNameElement = document.getElementById('deleteProductName');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteCancelledModal() {
    const modal = document.getElementById('deleteCancelledModal');
    modal.classList.remove('active');
}

// Close modals when clicking outside
document.getElementById('responseModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRespondModal();
    }
});

document.getElementById('resendModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeResendModal();
    }
});

document.getElementById('deleteCancelledModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteCancelledModal();
    }
});
</script>
@endpush
@endsection
