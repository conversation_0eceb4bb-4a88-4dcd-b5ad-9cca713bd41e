<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\ReturnModel;
use App\Models\Supplier;
use Carbon\Carbon;
use App\Traits\SupplierHelper;

class SupplierDashboardController extends Controller
{
    use SupplierHelper;
    /**
     * Display the supplier dashboard.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get current month for default filtering
        $currentMonth = Carbon::now()->format('Y-m');
        $filterMonth = $request->get('month', $currentMonth);

        // Parse the filter month
        $startDate = Carbon::createFromFormat('Y-m', $filterMonth)->startOfMonth();
        $endDate = Carbon::createFromFormat('Y-m', $filterMonth)->endOfMonth();

        // Get delivery statistics for this supplier
        $deliveryStats = [
            'total_deliveries' => SupplierDelivery::where('supplier_id', $supplier->id)
                ->whereBetween('delivery_date', [$startDate, $endDate])->count(),
            'pending_deliveries' => SupplierDelivery::where('supplier_id', $supplier->id)
                ->whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'pending')->count(),
            'received_deliveries' => SupplierDelivery::where('supplier_id', $supplier->id)
                ->whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'received')->count(),
            'partial_deliveries' => SupplierDelivery::where('supplier_id', $supplier->id)
                ->whereBetween('delivery_date', [$startDate, $endDate])
                ->where('status', 'partial')->count(),
        ];

        // Get return statistics for this supplier
        $returnStats = [
            'total_returns' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereBetween('return_date', [$startDate, $endDate])->count(),
            'pending_returns' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'requested')->count(),
            'approved_returns' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'approved')->count(),
            'completed_returns' => ReturnModel::where('supplier_id', $supplier->id)
                ->whereBetween('return_date', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
        ];

        // Get recent deliveries for this supplier
        $recentDeliveries = SupplierDelivery::with(['supplier', 'product'])
            ->where('supplier_id', $supplier->id)
            ->whereBetween('delivery_date', [$startDate, $endDate])
            ->orderBy('delivery_date', 'desc')
            ->limit(10)
            ->get();

        // Get recent returns for this supplier
        $recentReturns = ReturnModel::with(['product', 'store', 'supplier'])
            ->where('supplier_id', $supplier->id)
            ->whereBetween('return_date', [$startDate, $endDate])
            ->orderBy('return_date', 'desc')
            ->limit(10)
            ->get();

        // Get active suppliers count (global stat)
        $activeSuppliersCount = Supplier::where('status', 'active')->count();
        
        return view('supplier.dashboard', compact(
            'deliveryStats',
            'returnStats',
            'recentDeliveries',
            'recentReturns',
            'activeSuppliersCount',
            'filterMonth',
            'supplier'
        ));
    }
}
