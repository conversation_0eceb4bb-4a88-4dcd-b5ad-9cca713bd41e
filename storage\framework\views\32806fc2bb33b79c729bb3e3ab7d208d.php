<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> Toko - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', '<PERSON><PERSON><PERSON> Toko'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Ke<PERSON><PERSON></h1>
                    <p class="text-gray-600 mt-1">Monitor dan kelola <?php echo e($stats['total']); ?> toko Indah Berkah Abadi</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Tambah Toko
                    </a>
                    <a href="<?php echo e(route('admin.distributions.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                        Buat Distribusi
                    </a>
                    <a href="<?php echo e(route('admin.stores.export', request()->query())); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Export Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Store Stats - Compact Version -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['total'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Total Toko</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['active'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Toko Aktif</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['pending_distributions'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Distribusi Pending</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e($stats['total_distributions_today'] ?? 0); ?></div>
                        <div class="text-xs text-gray-600">Distribusi Hari Ini</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.stores')); ?>">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cari Toko</label>
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="Nama atau lokasi toko..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Wilayah</label>
                        <select name="region" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">Semua Wilayah</option>
                            <?php $__currentLoopData = $regions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($region); ?>" <?php echo e(request('region') == $region ? 'selected' : ''); ?>><?php echo e($region); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Stores Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Toko</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-200">
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Nama Toko</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Lokasi</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Email</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Distribusi Terakhir</th>
                            <th class="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                            <th class="text-center py-3 px-4 font-medium text-gray-700">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <?php
                            // Get recent distribution for this store
                            $lastDistribution = \App\Models\Distribution::where('store_id', $store->id)
                                                                       ->orderBy('created_at', 'desc')
                                                                       ->first();
                            $lastDelivery = $lastDistribution ? $lastDistribution->created_at->diffInDays() : null;

                            // Get pending distributions for this store
                            $pendingDistributions = \App\Models\Distribution::where('store_id', $store->id)
                                                                           ->where('confirmed', false)
                                                                           ->count();
                        ?>
                        <tr class="border-b border-gray-100 hover:bg-gray-50">
                            <td class="py-3 px-4">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium text-gray-900"><?php echo e($store->name); ?></div>
                                        <?php if($pendingDistributions > 0): ?>
                                            <div class="text-xs text-orange-600"><?php echo e($pendingDistributions); ?> distribusi pending</div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3 px-4 text-sm text-gray-600"><?php echo e($store->store_location); ?></td>
                            <td class="py-3 px-4 text-sm text-gray-600"><?php echo e($store->email); ?></td>
                            <td class="py-3 px-4 text-sm text-gray-600">
                                <?php if($lastDelivery !== null): ?>
                                    <?php echo e($lastDelivery); ?> hari lalu
                                <?php else: ?>
                                    <span class="text-gray-400">Belum ada</span>
                                <?php endif; ?>
                            </td>
                            <td class="py-3 px-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                                    Aktif
                                </span>
                            </td>
                            <td class="py-3 px-4">
                                <div class="flex justify-center gap-2">
                                    <a href="<?php echo e(route('admin.users.show', $store)); ?>"
                                       class="admin-dashboard-btn admin-dashboard-btn-secondary text-xs px-3 py-1">
                                        Lihat Detail
                                    </a>
                                    <a href="<?php echo e(route('admin.distributions.create', ['store_id' => $store->id])); ?>"
                                       class="admin-dashboard-btn admin-dashboard-btn-primary text-xs px-3 py-1">
                                        Distribusi
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="py-8 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <p class="text-lg font-medium">Tidak ada toko ditemukan</p>
                                    <p class="text-sm">Coba ubah filter pencarian Anda</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if($stores->hasPages()): ?>
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <?php echo e($stores->links('pagination.admin-dashboard')); ?>

        </div>
    </div>
    <?php endif; ?>



    <!-- Quick Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Aksi Cepat</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="<?php echo e(route('admin.users.create')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="admin-dashboard-stat-icon blue mr-4" style="width: 40px; height: 40px; margin-bottom: 0;">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                    </div>
                    <div class="text-left">
                        <h3 class="font-semibold text-gray-900">Tambah Toko</h3>
                        <p class="text-sm text-gray-600">Daftarkan toko baru</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.distributions.create')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="admin-dashboard-stat-icon green mr-4" style="width: 40px; height: 40px; margin-bottom: 0;">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div class="text-left">
                        <h3 class="font-semibold text-gray-900">Buat Distribusi</h3>
                        <p class="text-sm text-gray-600">Distribusi ke toko</p>
                    </div>
                </a>

                <a href="<?php echo e(route('admin.distributions.index')); ?>" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div class="admin-dashboard-stat-icon orange mr-4" style="width: 40px; height: 40px; margin-bottom: 0;">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                    </div>
                    <div class="text-left">
                        <h3 class="font-semibold text-gray-900">Monitor Distribusi</h3>
                        <p class="text-sm text-gray-600">Pantau pengiriman</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/stores.blade.php ENDPATH**/ ?>